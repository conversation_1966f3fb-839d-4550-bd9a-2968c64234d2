extension EmailValidator on String {
  bool isValidEmail() {
    return RegExp(
      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$',
    ).hasMatch(this);
  }

  bool get isPhoneNoValid {
    final phone = this[0] != "0" ?  "0$this" : this;

    final regExp = RegExp(r'(^(?:[+0]9)?[0-9]{10,12}$)');
    return regExp.hasMatch(phone);
  }

  bool get containsUppercase => contains(RegExp(r'[A-Z]'));

  bool get containsSpecialCharacter {
    final specialCharRegex = RegExp(
      r'[\^$*.\[\]{}()?\-"!@#%&/\,><:;_~`+='
      "'"
      ']',
    );
    return specialCharRegex.hasMatch(this);
  }

  String get formattedPhoneNumber {
    final formattedPhoneNumber = trim();
    return formattedPhoneNumber[0] == "0"
        ? formattedPhoneNumber.substring(1)
        : formattedPhoneNumber;
  }
}

  // bool checkEthereumAddress() {
  //   try {
  //     EthereumAddress.fromHex(this);
  //     return true;
  //   } catch (e) {
  //     return false;
  //   }
  // }
