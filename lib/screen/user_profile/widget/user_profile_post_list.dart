import 'package:flutter/material.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_nft_tab.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_repost_card.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class UserProfilePostList extends StatefulWidget {
  const UserProfilePostList({super.key});

  @override
  State<UserProfilePostList> createState() => _UserProfilePostListState();
}

class _UserProfilePostListState extends State<UserProfilePostList> {
  int _tabIndex = 0;
  final tabs = ["Posts", "Repost", "Collected NFT"];

  // Danh sách repost mẫu
  List<PostModel> get reposts => [
    PostModel(
      id: '${DateTime.now().microsecondsSinceEpoch}_1',
      user: PostUserModel(
        id: 'alice',
        fullName: 'Alice',
        avatar: 'https://randomuser.me/api/portraits/women/47.jpg',
      ),
      content: 'A video generated by AI is very impressive.',
      mediaUrls: [],
      reposts: 0,
      comments: 0,
      reactions: null,
      privacy: 'public',
      createdAt: '2h',
      updatedAt: '2h',
    ),
    PostModel(
      id: '${DateTime.now().microsecondsSinceEpoch}_2',
      user: PostUserModel(
        id: 'albert',
        fullName: 'Albert Flores',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      ),
      content: 'Check out this AI- gennnerated artwork!',
      mediaUrls: [
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80',
      ],
      reposts: 12,
      comments: 5,
      reactions: null,
      privacy: 'public',
      createdAt: '2h',
      updatedAt: '2h',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tabs
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: List.generate(tabs.length, (i) {
              final selected = i == _tabIndex;
              return GestureDetector(
                onTap: () => setState(() => _tabIndex = i),
                child: Padding(
                  padding: const EdgeInsets.only(right: 24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        tabs[i],
                        style:
                            selected
                                ? titleLarge.copyWith(
                                  color: themeData.neutral800,
                                  fontWeight: FontWeight.bold,
                                )
                                : titleLarge.copyWith(
                                  color: themeData.neutral400,
                                ),
                      ),
                      if (selected)
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          height: 3,
                          width: 28,
                          decoration: BoxDecoration(
                            color: themeData.primaryGreen500,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }),
          ),
        ),
        const SizedBox(height: 8),
        // Danh sách post mẫu
        if (_tabIndex == 0) ...[
          _PostCard(),
          _PostCard(),
          _PostCard(),
          _PostCard(),
        ] else if (_tabIndex == 1) ...[
          ...reposts.map((post) => UserProfileRepostCard(post: post)),
        ] else ...[
          const UserProfileNftTab(),
        ],
      ],
    );
  }
}

class _PostCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final post = PostModel(
      id: '${DateTime.now().microsecondsSinceEpoch}_card',
      user: PostUserModel(
        id: 'alice',
        fullName: 'Alice',
        avatar: 'https://randomuser.me/api/portraits/women/47.jpg',
      ),
      content: 'Check out this AI- gennnerated artwork!',
      mediaUrls: [
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80',
      ],
      reposts: 12,
      comments: 5,
      reactions: null,
      privacy: 'public',
      createdAt: '2h',
      updatedAt: '2h',
    );
    return HomeItemPostWidget(post: post, isNotOnTap: true);
  }
}
