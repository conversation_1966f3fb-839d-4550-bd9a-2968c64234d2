import 'package:flutter/material.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';

class UserProfileRepostCard extends StatelessWidget {
  final PostModel post;
  const UserProfileRepostCard({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return HomeItemPostWidget(post: post, isNotOnTap: true);
  }
}
