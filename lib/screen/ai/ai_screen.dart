import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mlx_flutter/mlx_flutter.dart';
import 'package:toii_social/screen/ai/chat_details_screen.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class AiScreen extends StatefulWidget {
  const AiScreen({super.key});

  @override
  State<AiScreen> createState() => _AiScreenState();
}

class _AiScreenState extends State<AiScreen> {
  String _platformVersion = 'Loading model...';
  bool isLoading = false;
  bool isSuccess = false;
  double _progressValue = 0.0;
  late StreamSubscription _streamSubscription;
  @override
  void initState() {
    super.initState();
    getData();
    _streamSubscription = GetIt.instance<MlxFlutter>().streamData.listen((progress) {
      setState(() {
        _progressValue = progress / 100.0;
      });
    });
  }

  @override
  void dispose() {
    _streamSubscription.cancel();
    super.dispose();
  }

  void getData() async {
    setState(() {
      isLoading = true;
      _platformVersion = 'Loading model...';
    });
    final result = await GetIt.instance<MlxFlutter>().loadModel();
    setState(() {
      isLoading = false;
      if (result == "loaded" || _progressValue == 1.0) {
        isSuccess = true;
        _platformVersion = 'Model loaded successfully';
      } else {
        _platformVersion = 'Failed to load model.';
      }
    });
  }

  Widget _bodyWidget() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('$_platformVersion\n'),
          if (isLoading) const SizedBox(height: 20),
          if (isLoading) const CircularProgressIndicator(),
          if (isLoading)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: LinearProgressIndicator(
                backgroundColor: Colors.cyanAccent,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                value: _progressValue,
              ),
            ),
          if (isLoading) Text('${(_progressValue * 100).round()}%'),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      resizeToAvoidBottomInset: true,
      title: Text(''),
      body: _bodyWidget(),
      floatingActionButton:
          isSuccess
              ? FloatingActionButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => ChatDetailsScreen(),
                    ),
                  );
                },
                child: const Icon(Icons.chat),
              )
              : null,
    );
  }
}
