import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_sign_in/google_sign_in.dart';
//import 'package:reown_appkit/reown_appkit.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:toii_social/cubit/auth/login/bloc/metamask_auth_bloc.dart';
import 'package:toii_social/cubit/auth/login/bloc/wallet_event.dart';
import 'package:toii_social/cubit/auth/login/bloc/wallet_state.dart';
import 'package:toii_social/cubit/auth/login/login/login_cubit.dart';
import 'package:toii_social/cubit/auth/login/validate/login_validate_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/bio/authentication_service.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';
import 'package:toii_social/utils/widget/dismiss_keyboard_widget.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _userNameController = TextEditingController();
  final _passwordController = TextEditingController();

  final _loginValidateCubit = LoginValidateCubit();
  final _loginCubit = LoginCubit();

  final FocusNode _focusUserName = FocusNode();
  final FocusNode _focusPassword = FocusNode();

  // late ReownAppKitModal _appKitModal;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    // Optional clientId
    // clientId: 'your-client_id.apps.googleusercontent.com',
    scopes: <String>[
      'email',
      'https://www.googleapis.com/auth/contacts.readonly',
    ],
  );
  @override
  void initState() {
    super.initState();
    _focusUserName.addListener(_onFocusChangeUserName);
    _focusPassword.addListener(_onFocusChangePassword);
    // _appKitModal = ReownAppKitModal(
    //   context: context,
    //   projectId: 'ca805f65affab172a9e433d13efb9024',
    //   metadata: const PairingMetadata(
    //     name: 'Example App',
    //     description: 'Example app description',
    //     url: 'https://reown.com/',
    //     icons: ['https://reown.com/logo.png'],
    //     redirect: Redirect(
    //       native: 'exampleapp://',
    //       universal: 'https://reown.com/exampleapp',
    //     ),
    //   ),
    //   featuresConfig: FeaturesConfig(
    //     socials: [
    //       AppKitSocialOption.Discord,
    //       AppKitSocialOption.Apple,
    //       AppKitSocialOption.Telegram,
    //       AppKitSocialOption.Google,
    //       AppKitSocialOption.Email, // optional: shows email login
    //     ],
    //   ),
    // );

    // _appKitModal.init().then((value) => setState(() {}));
    // _appKitModal.onModalConnect.subscribe(_onModalConnect);
    // _appKitModal.appKit!.onSessionAuthResponse.subscribe(
    //   _onSessionAuthResponse,
    // );
    _getData();
    loginBio();
  }

  // void _onSessionAuthResponse(SessionAuthResponse? response) {
  //   if (response?.session != null) {
  //     print(response);
  //   }
  // }

  // void _onModalConnect(ModalConnect? event) async {
  //   print(event);
  // }

  @override
  void dispose() {
    _userNameController.dispose();
    _passwordController.dispose();
    super.dispose();
    _focusUserName.removeListener(_onFocusChangeUserName);
    _focusUserName.dispose();
    _focusPassword.removeListener(_onFocusChangePassword);
    _focusPassword.dispose();
  }

  void _getData() async {
    final userName = await KeychainService.instance.getUserName();
    final password = await KeychainService.instance.getPasswordUser();
    _userNameController.text = userName;
  }

  void loginBio() async {
    final userName = await KeychainService.instance.getUserName();
    final password = await KeychainService.instance.getPasswordUser();
    if (userName.isNotEmpty && password.isNotEmpty) {
      final authenticationService = AuthenticationService(
        onLoginSuccess: () async {
          KeychainService.instance.setEnableBio(false);
          if (mounted) {
            _loginCubit.login(userName: userName, password: password);
          }
        },
      );
      authenticationService.authCheck(context);
    }
  }

  void _onFocusChangeUserName() {
    if (_focusUserName.hasFocus) {
      _loginValidateCubit.clearErrorEmail();
    } else {
      _loginValidateCubit.validateForm(email: _userNameController.text);
    }
  }

  void _onFocusChangePassword() {
    if (_focusPassword.hasFocus) {
      _loginValidateCubit.clearErrorPassword();
    } else {
      _loginValidateCubit.validateForm(password: _passwordController.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => MetaMaskAuthBloc()),
        BlocProvider(create: (_) => _loginValidateCubit),
        BlocProvider(create: (_) => _loginCubit),
      ],

      child: DismissKeyboardWidget(
        child: BaseScaffold(
          showLeading: false,
          title: Text(""),
          body: SafeArea(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(height: 12),
                    Text(
                      S.current.login_title,
                      style: headlineSmall.copyColor(themeData.neutral800),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      S.current.login_description,
                      style: bodyMedium.copyColor(themeData.neutral400),
                    ),
                    const SizedBox(height: 46),

                    BlocBuilder<LoginValidateCubit, LoginValidateState>(
                      builder: (context, state) {
                        return TTextField(
                          labelText: S.current.login_email_or_phone,
                          hintText: S.current.login_email_or_phone,
                          focusNode: _focusUserName,
                          onEditingComplete: () {
                            context.read<LoginValidateCubit>().validateForm(
                              email: _userNameController.text,
                            );
                            _focusUserName.unfocus();
                          },
                          // onSaved: (value) {
                          //   context.read<LoginValidateCubit>().validateForm(
                          //     email: _userNameController.text,
                          //   );
                          // },
                          errorText:
                              state.errorEmail?.isNotEmpty == true
                                  ? state.errorEmail
                                  : null,
                          textController: _userNameController,
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    BlocBuilder<LoginValidateCubit, LoginValidateState>(
                      builder: (context, state) {
                        return TTextField(
                          hintText: S.current.login_password,
                          labelText: S.current.login_password,
                          focusNode: _focusPassword,
                          onEditingComplete: () {
                            context.read<LoginValidateCubit>().validateForm(
                              password: _passwordController.text,
                            );
                            _focusPassword.unfocus();
                          },

                          obscureText: !state.isShowPassword,
                          textController: _passwordController,
                          errorText:
                              state.errorPassword?.isNotEmpty == true
                                  ? state.errorPassword
                                  : null,
                          suffixIcon: IconButton(
                            icon: SvgPicture.asset(
                              !state.isShowPassword
                                  ? Assets.icons.eyeHide.path
                                  : Assets.icons.eyeShow.path,
                            ),
                            onPressed: () {
                              _loginValidateCubit.toggleShowPassword();
                            },
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        S.current.login_forgot_password,
                        style: titleSmall.copyColor(themeData.neutral800),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(child: _loginButtonWidget()),
                        const SizedBox(width: 12),
                        SvgPicture.asset(
                          Assets.icons.icFaceId.path,
                          width: 48,
                          height: 48,
                        ),
                      ],
                    ),
                    const SizedBox(height: 36),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 1,
                            color: themeData.neutral200,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(S.current.login_or_sign_in_with),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Container(
                            height: 1,
                            color: themeData.neutral200,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 36),
                    _socialListWidget(),
                    const SizedBox(height: 36),
                    _loginWithWalletWidget(),
                    const SizedBox(height: 36),

                    _registerWidget(),
                    const SizedBox(height: 36),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _loginButtonWidget() {
    return BlocConsumer<LoginCubit, LoginState>(
      listener: (context, state) async {
        if (state.status.isFailure) {
          _loginValidateCubit.setErrorWhenLogin(
            state.message ?? "Login not success",
          );
        }
        if (state.status.isSuccess) {
          KeychainService.instance.setUserName(_userNameController.text);
          KeychainService.instance.setPassword(_passwordController.text);
          // if (state.login?.profile != null) {
          //   GetIt.instance<ProfileCubit>().updateProfile(state.login!.profile!);
          // }
          // context.showToast(title: "Login success");

          context.go(RouterEnums.inital.routeName);
        }
        if (state.status.isNewUser) {
          context.push(RouterEnums.registerCreateName.routeName);
        }
      },
      builder: (context, state) {
        return TSButton.primary(
          isLoading: state.status.isLoading,
          title: S.current.start_login,
          onPressed: () {
            if (_loginValidateCubit.validateForm(
                  email: _userNameController.text,
                  password: _passwordController.text,
                ) ==
                true) {
              _loginValidateCubit.clearErrorEmail();
              _loginValidateCubit.clearErrorPassword();
              context.read<LoginCubit>().login(
                userName: _userNameController.text,
                password: _passwordController.text,
              );
            }
          },
        );
      },
    );
  }

  Widget _socialListWidget() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: () {
            _handleSignIn();
          },
          child: SvgPicture.asset(Assets.icons.icGmail.path),
        ),
        const SizedBox(width: 32),
        GestureDetector(
          onTap: () async {
            final credential = await SignInWithApple.getAppleIDCredential(
              scopes: [
                AppleIDAuthorizationScopes.email,
                AppleIDAuthorizationScopes.fullName,
              ],
            );
            _loginCubit.loginWithApple(idToken: credential.identityToken!);
          },
          child: SvgPicture.asset(Assets.icons.icApple.path),
        ),
        //   const SizedBox(width: 32),
        // SvgPicture.asset(Assets.icons.icDiscord),
      ],
    );
  }

  Widget _loginWithWalletWidget() {
    return BlocListener<MetaMaskAuthBloc, WalletState>(
      listener: (context, state) async {
        if (state is WalletErrorState) {
          //  hideDialog(dialogContext);
          context.showSnackbar(message: state.message);
        } else if (state is WalletReceivedSignatureState) {
          _loginCubit.loginWithWallet(
            address: state.walletAddress,
            signature: state.signatureFromWallet,
          );
          // Hide loading dialog and show success message
          // context.showSnackbar(message: state.message.toString());
          //  print(state.signatureFromWallet);
          // Save wallet connection status and other details in SharedPreferences
          // SharedPreferences prefs = await SharedPreferences.getInstance();
          // await prefs.setBool(
          //   'isWalletConnected',
          //   true,
          // ); // Flag for wallet connection
          // await prefs.setString(
          //   'signature',
          //   state.walletAddress,
          // ); // Save signature if needed
          // await prefs.setString(
          //   'yy',
          //   state.signatureFromWallet,
          // ); // Save signature if needed

          // Navigate to HomeScreen (with Bottom Navigation Bar)
        }
      },
      child: BlocBuilder<MetaMaskAuthBloc, WalletState>(
        builder: (context, state) {
          return GestureDetector(
            onTap: () {
              final String signatureFromBackend =
                  "NonStop IO Technologies Pvt Ltd.";

              context.read<MetaMaskAuthBloc>().add(
                MetamaskAuthEvent(signatureFromBackend: signatureFromBackend),
              );
            },
            child: SizedBox(
              height: 48,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    S.current.login_with_wallet,
                    style: titleMedium.copyColor(themeData.neutral800),
                  ),
                  const SizedBox(width: 6),
                  SvgPicture.asset(Assets.icons.icArrowRight.path),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _registerWidget() {
    return DismissKeyboardWidget(
      onTap: () {
        context.push(RouterEnums.register.routeName);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            S.current.login_dont_have_account,
            style: titleMedium.copyColor(themeData.neutral400),
          ),
          const SizedBox(width: 4),
          Text(
            S.current.login_register,
            style: titleMedium.copyColor(themeData.primaryGreen500),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSignIn() async {
    try {
      _googleSignIn.disconnect();
      final result = await _googleSignIn.signIn();
      final authentication = await result?.authentication;
      if (authentication != null) {
        _loginCubit.loginWithGmail(
          accessToken: authentication.accessToken!,
          idToken: authentication.idToken!,
        );
      }
    } catch (error) {
      context.showSnackbar(message: error.toString());
    }
  }

  void loginWithWallet() async {
    // final session = _appKitModal.session;
    // final address =
    //     session!.namespaces!['eip155']?.accounts.first.split(':').last;
    // print(address);
    // final message = "Hello from Reown!";

    // final signature = await _appKitModal.request(
    //   topic: session.topic,
    //   chainId: 'eip155:1',
    //   request: SessionRequestParams(method: 'personal_sign', params: [address]),
    // );
    // print(signature);
    // return;
    // final session = _appKitModal.session;
    // if (session == null) {
    //   // Handle case when session is not available
    //   return;
    // }
    // final address =
    //     session.namespaces!['eip155']?.accounts.first.split(':').last;
    // final message = "Hello from Reown!";

    // try {
    //   final signature = await _appKitModal.request(
    //     topic: session.topic,
    //     chainId: 'eip155:1',
    //     request: SessionRequestParams(
    //       method: 'personal_sign',
    //       params: [message, address],
    //     ),
    //   );
    //   print('Signature: $signature');
    // } catch (error) {
    //   print('Error signing message: $error');
    // }
  }
}
