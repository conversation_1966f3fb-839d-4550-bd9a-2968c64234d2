import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/core/constant/key_shared.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Widget _buildBody() {
    return Center(
      child: InkWell(
        onTap: () {
          SharedPref.setBool(KeyShared.isLogin, false);
          SharedPref().clearKey(KeyShared.tokenKey);
          KeychainService.instance.clearAll();
          context.go(RouterEnums.inital.routeName);
        },
        child: Text(
          'Logout',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(title: Text("Profile"), body: _buildBody());
  }
}
