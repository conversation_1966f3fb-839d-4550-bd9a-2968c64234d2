import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class BottomSheetMoreAction extends StatelessWidget {
  const BottomSheetMoreAction({super.key, this.onEdit, this.onDelete});

  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 12),
        _ActionItem(
          icon: Assets.icons.icSaveAdd.svg(),
          text: 'Save',
          onTap: () {
            Navigator.pop(context);
          },
        ),
        _ActionItem(
          icon: Assets.icons.icMore.svg(),
          text: 'Edit post',
          onTap: onEdit ?? () => Navigator.pop(context),
        ),
        _ActionItem(
          icon: Assets.icons.icCloseSquare.svg(),
          text: 'Hide post for me',
          onTap: () {
            Navigator.pop(context);
          },
        ),
        _ActionItem(
          icon: Assets.icons.icWarning.svg(),
          text: 'Report post',
          onTap: () {
            Navigator.pop(context);
          },
        ),
        _ActionItem(
          icon: Assets.icons.icCopy.svg(),
          text: 'Copy post text',
          onTap: () {
            Navigator.pop(context);
          },
        ),
        _ActionItem(
          icon: Assets.icons.icCloseSquare.svg(),
          text: 'Delete post',
          onTap: onDelete ?? () => Navigator.pop(context),
        ),
        const SizedBox(height: 12),
      ],
    );
  }
}

class _ActionItem extends StatelessWidget {
  final Widget icon;
  final String text;
  final VoidCallback onTap;
  const _ActionItem({
    required this.icon,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            icon,
            const SizedBox(width: 16),
            Text(text, style: titleMedium.copyColor(themeData.neutral800)),
          ],
        ),
      ),
    );
  }
}
