import 'dart:io';

import 'package:flutter/material.dart';
import 'package:toii_social/screen/home/<USER>/detail_preview_img.dart';

class ImageGrid extends StatelessWidget {
  final List<String> imagePaths;
  final void Function(int index) onRemove;
  const ImageGrid({
    super.key,
    required this.imagePaths,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    if (imagePaths.isEmpty) return const SizedBox.shrink();

    List<Widget> rows = [];
    int i = 0;
    while (i < imagePaths.length) {
      if (i == imagePaths.length - 1) {
        rows.add(Row(children: [Expanded(child: _buildImageItem(context, i))]));
        break;
      }
      rows.add(
        Row(
          children: [
            Expanded(child: _buildImageItem(context, i)),
            const SizedBox(width: 4),
            if (i + 1 < imagePaths.length)
              Expanded(child: _buildImageItem(context, i + 1)),
          ],
        ),
      );
      i += 2;
    }
    return Column(
      children: [
        for (int j = 0; j < rows.length; j++) ...[
          rows[j],
          if (j != rows.length - 1) const SizedBox(height: 4),
        ],
      ],
    );
  }

  Widget _buildImageItem(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (_) => DetailPreviewImg(
                  arg: DetailPreviewImgArg(
                    images: imagePaths,
                    initialIndex: index,
                    post: null,
                  ),
                ),
          ),
        );
      },
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: AspectRatio(
              aspectRatio: 1,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: FileImage(File(imagePaths[index])),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => onRemove(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
