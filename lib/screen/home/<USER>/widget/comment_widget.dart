import 'package:flutter/material.dart';
import 'package:readmore/readmore.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/screen/home/<USER>/widget/like_comment_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';

class CommentWidget extends StatelessWidget {
  final CommentItemModel commentItemModel;

  const CommentWidget({super.key, required this.commentItemModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeData.neutral200,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar
          AvatarWidget(
            name:
                commentItemModel.user?.username ??
                commentItemModel.user?.fullName ??
                "",
            imageUrl: commentItemModel.user?.avatar ?? "",
          ),
          const SizedBox(width: 12),
          // Comment Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Username and Time
                Row(
                  children: [
                    Text(
                      commentItemModel.user?.username ??
                          commentItemModel.user?.fullName ??
                          "",
                      style: titleSmall.copyColor(themeData.neutral800),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '· ${commentItemModel.updatedAtTime}',
                      style: bodySmall.copyColor(themeData.neutral300),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                // Comment Text
                // showSeeMore
                ReadMoreText(
                  commentItemModel.content,
                  trimLines: 2,
                  trimMode: TrimMode.Line,
                  trimCollapsedText: ' See more',
                  trimExpandedText: ' Show less',
                  style: bodyMedium.copyColor(themeData.neutral800),
                  moreStyle: labelLarge.copyColor(themeData.primaryGreen500),
                ),

                const SizedBox(height: 8),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Reply',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(width: 12),
          LikeCommentWidget(commentItemModel: commentItemModel),
        ],
      ),
    );
  }
}
