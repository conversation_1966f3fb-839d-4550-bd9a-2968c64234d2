import 'package:flutter/material.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class PostIconText extends StatelessWidget {
  final Widget icon;
  final String text;
  const PostIconText({super.key, required this.icon, required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        icon,
        const SizedBox(width: 4),
        Text(text, style: bodySmall.copyWith(color: Colors.white)),
      ],
    );
  }
}
