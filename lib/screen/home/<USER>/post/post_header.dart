import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/bottom_sheet_more_action.dart';
import 'package:toii_social/utils/time_utils.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';

class PostHeader extends StatelessWidget {
  final PostModel post;
  const PostHeader({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    final theme = context.themeData;
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              context.push(
                RouterEnums.userProfile.routeName,
                extra: post.user?.id,
              );
            },
            child: AvatarWidget(
              size: 40,
              imageUrl: post.user?.avatar,
              name: post.user?.fullName ?? '',
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        post.user?.username ?? post.user?.fullName ?? '',
                        style: titleMedium.copyWith(color: theme.neutral800),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Container(
                      width: 3,
                      height: 3,
                      decoration: BoxDecoration(
                        color: theme.neutral300,
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Flow',
                      style: titleMedium.copyWith(color: theme.primaryGreen500),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  timeAgoSinceDate(post.updatedAt ?? ''),
                  style: labelMedium.copyWith(color: theme.neutral300),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              showTtBottomSheet(
                context,
                child: BottomSheetMoreAction(
                  onEdit: () {
                    context
                        .push(RouterEnums.createPost.routeName, extra: post)
                        .then((value) {
                          context.read<HomeCubit>().getUserFeed();
                        });
                  },
                  onDelete: () {},
                ),
                isShowClose: true,
                isDismissible: true,
              );
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 0, 0, 0),
              child: SvgPicture.asset(
                Assets.icons.icMore.path,
                colorFilter: ColorFilter.mode(
                  theme.neutral500,
                  BlendMode.srcIn,
                ),
                width: 24,
                height: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
