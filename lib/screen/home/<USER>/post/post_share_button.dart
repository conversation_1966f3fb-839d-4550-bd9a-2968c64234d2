import 'dart:async';

import 'package:flutter/material.dart';
import 'package:toii_social/core/service/share_service.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/post/post_icon_text.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class PostShareButton extends StatefulWidget {
  final PostModel post;
  final bool showText;
  final Color? iconColor;
  final double? iconSize;

  const PostShareButton({
    super.key,
    required this.post,
    this.showText = true,
    this.iconColor,
    this.iconSize,
  });

  @override
  State<PostShareButton> createState() => _PostShareButtonState();
}

class _PostShareButtonState extends State<PostShareButton> {
  Timer? _debounceTimer;
  final bool _isSharing = false;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _handleShare() async {
    await _copyLink();
   
  }

  

  Future<void> _sharePost() async {
    try {
      // Show loading indicator briefly
      final messenger = ScaffoldMessenger.of(context);
      messenger.showSnackBar(
        const SnackBar(
          content: Text('Preparing to share...'),
          duration: Duration(milliseconds: 500),
        ),
      );

      final result = await ShareService.sharePost(widget.post);
      if (mounted) {
        final message =
            result.status.name == 'success'
                ? 'Post shared successfully!'
                : 'Share completed';
        messenger.showSnackBar(SnackBar(content: Text(message)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Failed to share post')));
      }
    }
  }

  Future<void> _copyLink() async {
    try {
      final result = await ShareService.sharePostUrl(widget.post);
      // if (mounted) {
      //   final message =
      //       result.status.name == 'success'
      //           ? 'Link shared successfully!'
      //           : 'Link share completed';
      //   context.showSnackbar(message: message);
      // }
    } catch (e) {
      if (mounted) {
        context.showSnackbar(message: 'Failed to share link');
      }
    }
  }

  Future<void> _shareWithCustomText() async {
    final TextEditingController controller = TextEditingController();

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Add Your Message'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: 'Write your message here...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                navigator.pop();
                try {
                  final result = await ShareService.sharePostWithText(
                    widget.post,
                    controller.text.trim(),
                  );
                  if (mounted) {
                    final message =
                        result.status.name == 'success'
                            ? 'Post shared with your message!'
                            : 'Share completed';
                    messenger.showSnackBar(SnackBar(content: Text(message)));
                  }
                } catch (e) {
                  if (mounted) {
                    messenger.showSnackBar(
                      const SnackBar(content: Text('Failed to share post')),
                    );
                  }
                }
              },
              child: const Text('Share'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showText) {
      return GestureDetector(
        onTap: _handleShare,
        child: PostIconText(
          icon:
              _isSharing
                  ? SizedBox(
                    width: widget.iconSize ?? 20,
                    height: widget.iconSize ?? 20,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : Assets.icons.icShare.svg(
                    width: widget.iconSize ?? 20,
                    height: widget.iconSize ?? 20,
                    colorFilter:
                        widget.iconColor != null
                            ? ColorFilter.mode(
                              widget.iconColor!,
                              BlendMode.srcIn,
                            )
                            : null,
                  ),
          text: widget.post.getViewRepost,
        ),
      );
    }

    return IconButton(
      onPressed: _isSharing ? null : _handleShare,
      icon:
          _isSharing
              ? SizedBox(
                width: widget.iconSize ?? 24,
                height: widget.iconSize ?? 24,
                child: const CircularProgressIndicator(strokeWidth: 2),
              )
              : Assets.icons.icShare.svg(
                width: widget.iconSize ?? 24,
                height: widget.iconSize ?? 24,
                colorFilter:
                    widget.iconColor != null
                        ? ColorFilter.mode(widget.iconColor!, BlendMode.srcIn)
                        : null,
              ),
    );
  }
}
