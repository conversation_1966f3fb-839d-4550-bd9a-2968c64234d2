import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/post/comment/comment_cubit.dart';
import 'package:toii_social/cubit/post/create_comment/create_comment_cubit.dart';
import 'package:toii_social/cubit/post/detail_post/detail_post_cubit.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/widget/comment_inputbar_widget.dart';
import 'package:toii_social/screen/home/<USER>/widget/comment_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class HomeDetailsScreen extends StatefulWidget {
  final PostModel postModel;
  const HomeDetailsScreen({super.key, required this.postModel});

  @override
  State<HomeDetailsScreen> createState() => _HomeDetailsScreenState();
}

class _HomeDetailsScreenState extends State<HomeDetailsScreen> {
  late CommentCubit _commentCubit;
  late DetailPostCubit _detailPostCubit;
  @override
  void initState() {
    _commentCubit = CommentCubit(postId: widget.postModel.id);
    _detailPostCubit = DetailPostCubit();
    _detailPostCubit.fetchPostDetail(widget.postModel.id);
    super.initState();
  }

  final TextEditingController _commentController = TextEditingController();

  Widget _bodyWidget() {
    return BlocBuilder<DetailPostCubit, DetailPostState>(
      bloc: _detailPostCubit,
      builder: (context, detailState) {
        if (detailState.status == DetailPostStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        } else if (detailState.status == DetailPostStatus.failure) {
          return Center(child: Text(detailState.errorMessage ?? ''));
        } else if (detailState.status == DetailPostStatus.success &&
            detailState.post != null) {
          return SafeArea(
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              child: Column(
                children: [
                  HomeItemPostWidget(post: detailState.post!, isNotOnTap: true),
                  BlocBuilder<CommentCubit, CommentState>(
                    builder: (context, state) {
                      if (state.status.isLoading) {
                        return const Center(child: CircularProgressIndicator());
                      } else if (state.status.isSuccess) {
                        return _commentListWidget(comments: state.comments);
                      } else if (state.status.isFailure) {
                        return Center(child: Text(state.errorMessage ?? ''));
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _commentListWidget({required List<CommentItemModel> comments}) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(top: 0),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: comments.length,
      itemBuilder: (context, index) {
        return CommentWidget(commentItemModel: comments[index]);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => _commentCubit..getComments()),
        BlocProvider(
          create: (_) => CreateCommentCubit(postId: widget.postModel.id),
        ),
        BlocProvider(create: (_) => _detailPostCubit),
      ],
      child: BaseScaffold(
        resizeToAvoidBottomInset: true,
        title: Text('Post'),
        appbar: BaseAppBar(
          title: ('Post'),
          actions: [
            IconButton(onPressed: () {}, icon: const Icon(Icons.more_horiz)),
          ],
        ),
        body: _bodyWidget(),
        bottom: BlocConsumer<CreateCommentCubit, CreateCommentState>(
          listener: (context, state) async {
            if (state.status.isSuccess) {
              context.showSnackbar(message: "Comment successfully");
              _commentCubit.addComment(state.comment!);
            }
          },
          builder: (context, state) {
            return Padding(
              padding: MediaQuery.of(context).viewInsets,
              child: CommentInputBarWidget(
                controller: _commentController,
                onSend: (value) {
                  context.read<CreateCommentCubit>().createAComment(
                    content: value,
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
