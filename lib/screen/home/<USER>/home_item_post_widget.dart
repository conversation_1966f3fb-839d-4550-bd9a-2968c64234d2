import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/post/post_action_bar.dart';
import 'package:toii_social/screen/home/<USER>/post/post_header.dart';
import 'package:toii_social/screen/home/<USER>/post/post_image_gallery.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class HomeItemPostWidget extends StatelessWidget {
  final PostModel post;
  final bool isNotOnTap;
  const HomeItemPostWidget({
    super.key,
    required this.post,
    required this.isNotOnTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.themeData;
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: theme.neutral200,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  PostHeader(post: post),
                  if (post.content.isNotEmpty)
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        isNotOnTap
                            ? null
                            : context.push(
                              RouterEnums.homeDetails.routeName,
                              extra: post,
                            );
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        child: Text(
                          post.content,
                          style: bodyLarge.copyWith(color: theme.neutral800),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                ],
              ),
              PostImageGallery(post: post),
              if (post.mediaUrls.isEmpty) const SizedBox(height: 16),
            ],
          ),
        ),
        PostActionBar(post: post),
      ],
    );
  }
}
