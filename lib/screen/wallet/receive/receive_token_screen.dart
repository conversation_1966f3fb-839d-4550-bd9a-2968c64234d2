import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:share_plus/share_plus.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/utils/utils.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class ReceiveTokenScreen extends StatelessWidget {
  final String address;
  const ReceiveTokenScreen({super.key, required this.address});

  Widget _description() {
    return Text(
      "Scan QR address to receive payment",
      textAlign: TextAlign.center,
    );
    // style: const TextStyle().interStyle.copyWith(
    //     height: 28 / 20,
    //     color: slate50,
    //     fontSize: 18,
    //     fontWeight: FontWeight.w600));
  }

  Widget _infoWallet(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 14),
              height: 48,
              decoration: BoxDecoration(
                //   color: neutral800,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(shortenAddress(address)),
                // style: const TextStyle().interStyle.copyWith(
                //     height: 28 / 20,
                //     color: white,
                //     fontSize: 18,
                //     fontWeight: FontWeight.w600)),
              ),
            ),
          ),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: () async {
              await Clipboard.setData(ClipboardData(text: address));
              // ignore: use_build_context_synchronously
              context.showSnackbar(message: "Copied to clipboard");
            },
            child: Container(
              height: 48,
              width: 48,
              decoration: BoxDecoration(
                //   color: neutral800,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(child: SvgPicture.asset(Assets.icons.icCopy.path)),
            ),
          ),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: () {
              Share.share(address);
            },
            child: Container(
              height: 48,
              width: 48,
              decoration: BoxDecoration(
                //     color: neutral800,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(child: SvgPicture.asset(Assets.icons.icShare.path)),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      title: Text("Receive"),
      body: Center(
        child: Column(
          children: [
            const SizedBox(height: 80),
            _description(),
            const SizedBox(height: 24),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                width: MediaQuery.of(context).size.width / 3 * 2,
                child: Container(
                  decoration: const BoxDecoration(color: Colors.black),
                  margin: const EdgeInsets.all(16),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: PrettyQr(
                      elementColor: themeData.white900,
                      data: address,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 60),
            _infoWallet(context),
          ],
        ),
      ),
    );
  }
}
