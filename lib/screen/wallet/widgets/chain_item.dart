import 'package:flutter/material.dart';
import 'package:toii_social/model/chain/chain_model.dart';

class ChainItem extends StatelessWidget {
  const ChainItem({super.key, required this.e});

  final ChainModel e;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        //   GetIt.instance<WalletCubit>().saveChainSelected(e);
        Navigator.of(context).pop(e);
      },
      child: Column(
        children: [
          Row(
            children: [
              /// Icon
              if (e.iconUrl != null) Image.network(e.iconUrl ?? '', width: 18),
              const SizedBox(width: 12),

              /// Chain name
              Expanded(
                child: Text(
                  e.name!,
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),

              /// Checked Icon
              // e.chainId ==
              //         GetIt.instance<WalletCubit>().currentActiveChain.chainId
              //     ? Icon(Icons.check, )
              //     : const SizedBox(
              //         width: 16,
              //       ),
            ],
          ),
          const SizedBox(height: 26),
        ],
      ),
    );
  }
}
