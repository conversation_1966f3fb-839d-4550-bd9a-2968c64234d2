import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

class SendTokenScreen extends StatefulWidget {
  final CryptocurrencyItem? token;
  const SendTokenScreen({super.key, this.token});

  @override
  State<SendTokenScreen> createState() => _SendTokenScreenState();
}

class _SendTokenScreenState extends State<SendTokenScreen> {
  final textFieldDecoration = InputDecoration(
    hintText: "Address",
    contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    hintStyle: const TextStyle()
        .interStyle
        .copyWith(color: zinc500, fontSize: 14, fontWeight: FontWeight.w400),
    focusedBorder: const OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(13)),
      borderSide: BorderSide.none,
    ),
    enabledBorder: const OutlineInputBorder(
        borderRadius: BorderRadius.all(Radius.circular(13)),
        borderSide: BorderSide.none),
    border: const OutlineInputBorder(
        borderRadius: BorderRadius.all(Radius.circular(13)),
        borderSide: BorderSide.none),
    isDense: true,
  );
  TextEditingController destinationTextController =
      TextEditingController(text: "");
  TextEditingController amountTextController = TextEditingController(text: "");

  String selectedTokenContract = "NATIVE";

  @override
  void initState() {
    super.initState();
  }
  //String gasFee = "";

  //bool isFetchingGasFee = false;

  // void fetchGasFee() async {
  //   setState(() {
  //     isFetchingGasFee = true;
  //     gasFee = "";
  //   });

  //   // Transaction Desc
  //   String destinationAddress = destinationTextController.text;
  //   EtherAmount? amount = EtherAmount.fromBigInt(
  //     EtherUnit.wei,
  //     BigInt.from(
  //       double.parse(
  //             amountTextController.text == "" ? "0" : amountTextController.text,
  //           ) *
  //           pow(10, 18),
  //     ),
  //   );

  //   // Fetch Gas Fee
  //   // EtherAmount gasEstimation = selectedTokenContract == "NATIVE"
  //   EtherAmount gasEstimation =
  //       await GetIt.instance<ChainRepository>().fetchEstimateGasFee(
  //     receipentPublicAddress: destinationAddress,
  //     chain: GetIt.instance<WalletCubit>().currentActiveChain,
  //   );
  //   // : await ChainRepository.fetchEstimateTokenTransferGasFee(
  //   //     senderPublicAddress:
  //   //         WalletController.to.currentActiveWallet.publicAddress!,
  //   //     receipentPublicAddress: destinationAddress,
  //   //     contractAddress: selectedTokenContract,
  //   //     chain: WalletController.to.currentActiveChain,
  //   //     value: amount,
  //   //   );

  //   setState(() {
  //     isFetchingGasFee = false;
  //     gasFee = gasEstimation
  //         .getValueInUnit(EtherUnit.ether)
  //         .toStringAsFixed(10)
  //         .toString();
  //   });
  // }

  bool isCanSubmit() {
    // if (gasFee == "") {
    //   return false;
    // }
    if (amountTextController.text == "" ||
        destinationTextController.text == "") {
      return false;
    }
    return true;
  }

  void reset() {
    destinationTextController.text = "";
    amountTextController.text = "";
    //  gasFee = "";
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (_) => SendTokenCubit(),
        child: Stack(children: [
          TangenScaffold(
            appBar: CustomAppBar(contentTitle: "Send"),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Address",
                    style: const TextStyle().interStyle.copyWith(
                        height: 28 / 16,
                        color: white,
                        fontSize: 16,
                        fontWeight: FontWeight.w400),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                    //  height: 40,
                    decoration: BoxDecoration(
                      color: neutral800,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            maxLines: 1,
                            minLines: 1,
                            style: const TextStyle().interStyle.copyWith(
                                color: white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500),
                            controller: destinationTextController,
                            decoration: textFieldDecoration,
                            onChanged: (val) {
                              //     fetchGasFee();
                            },
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            final result = await context.push(
                              RouterEnums.scanQr.routeName,
                            );
                            if (result is String) {
                              destinationTextController.text = result;
                            }
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(right: 12),
                            child: SvgPicture.asset(Assets.icQrcode),
                          ),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Text(
                    "Token",
                    style: const TextStyle().interStyle.copyWith(
                        height: 28 / 16,
                        color: white,
                        fontSize: 16,
                        fontWeight: FontWeight.w400),
                  ),
                  SizedBox(width: 100, child: _buildTokenSelect()),
                  const SizedBox(
                    height: 12,
                  ),
                  Text(
                    "Quantity",
                    style: const TextStyle().interStyle.copyWith(
                        height: 28 / 16,
                        color: white,
                        fontSize: 16,
                        fontWeight: FontWeight.w400),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 40,
                          decoration: BoxDecoration(
                            color: neutral800,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: TextFormField(
                            maxLines: 1,
                            minLines: 1,
                            style: const TextStyle().interStyle.copyWith(
                                color: white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500),
                            controller: amountTextController,
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                              signed: false,
                            ),
                            decoration: textFieldDecoration.copyWith(
                                hintText: "Amount"),
                            onChanged: (val) {
                              //fetchGasFee();
                            },
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      _buildMaxButton()
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Gas Fees (${GetIt.instance<WalletCubit>().currentActiveChain.symbol})",
                        style: TextStyle(
                          color: const Color(0xFF141414).withOpacity(0.4),
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      // isFetchingGasFee
                      //     ? const SizedBox(
                      //         width: 25,
                      //         height: 25,
                      //         child: CircularProgressIndicator())
                      //     : Text(
                      //         gasFee,
                      //         style: const TextStyle(
                      //           color: Color(0xFF141414),
                      //           fontSize: 18,
                      //           fontWeight: FontWeight.w600,
                      //         ),
                      //       ),
                    ],
                  ),
                ],
              ),
            ),
            bottom: _bottomWidget(context),
          ),
          BlocConsumer<SendTokenCubit, SendTokenState>(
              listener: (context, state) {
            if (state.errors != null) {}
          }, builder: (context, state) {
            return state.loading
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Loading(),
                        if (state.transactionId != null)
                          TransactionInfo(transactionId: state.transactionId!)
                        // explorerUrl: network.config.explorerUrl)
                      ],
                    ),
                  )
                : const SizedBox();
          })
        ]));
  }

  Widget _bottomWidget(BuildContext context) {
    return BlocBuilder<SendTokenCubit, SendTokenState>(
        builder: (context, state) {
      return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12)
              .copyWith(bottom: MediaQuery.of(context).padding.bottom, top: 0),
          child: Row(
            children: [
              Expanded(
                child: ButtonCustomWidget(
                  bgButtonColor: yellow400,
                  // bgButtonColor: .withOpacity(isCanSubmit() ? 1 : 0.3),
                  titleButton: "Submit Transaction",
                  onPressed: () async {
                    if (isCanSubmit() == false) {
                      return;
                    }
                    final walletCurrent =
                        GetIt.instance<WalletRepository>().walletCurrent();

                    ValidateCardService.authen(
                        walletCurrent?.walletType == "card",
                        onResult: (p0) async {
                      if (p0 != null) {
                        await NfcManager.instance.stopSession();
                        SystemChannels.textInput.invokeMethod('TextInput.hide');

                        String destinationAddress =
                            destinationTextController.text;
                        final contractAddressHex =
                            selectedTokenContract == "NATIVE"
                                ? null
                                : selectedTokenContract;
                        final transId = await context
                            .read<SendTokenCubit>()
                            .transfer(
                                contractAddressHex: contractAddressHex,
                                privateKey: p0.privateKeyHex,
                                destinationAddress: destinationAddress,
                                quantity: amountTextController.text);
                        if (transId != null) {
                          GetIt.instance<WalletCubit>().fetchBalanceFromChain();
                          if (mounted) {
                            // ignore: use_build_context_synchronously
                            Navigator.of(context).pop();

                            // ignore: use_build_context_synchronously
                            GetIt.instance<NavigationService>()
                                .navigatorContext
                                .showSnackBarSuccess(
                                    text: "Transaction Success",
                                    ontap: (value) {
                                      GetIt.instance<NavigationService>()
                                          .navigatorContext
                                          .push(
                                              RouterEnums
                                                  .transactionDetails.routeName,
                                              extra: transId);
                                    });
                          }
                        }
                      }
                    });
                  },
                ),
              ),
            ],
          ));
    });
  }

  Widget _buildMaxButton() {
    return InkWell(
      onTap: () {
        // if (selectedTokenContract == "NATIVE") {
        //   amountTextController.text =
        //       GetIt.instance<WalletCubit>().state.nativeBalance.toString();
        // } else {
        //   amountTextController.text = GetIt.instance<WalletCubit>()
        //       .state
        //       .tokens[selectedTokenContract]!
        //       .balance;
        // }
      },
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: white)),
        child: Center(
          child: Text(
            "MAX",
            style: const TextStyle().interStyle.copyWith(
                color: white, fontSize: 16, fontWeight: FontWeight.w500),
          ),
        ),
      ),
    );
  }

  Widget _buildTokenSelect() {
    //final tokens = GetIt.instance<WalletCubit>().state.tokens;

    return DropdownButtonFormField<String>(
      dropdownColor: neutral800,
      style: const TextStyle(
        overflow: TextOverflow.ellipsis,
        color: Colors.black,
      ),
      isDense: true,
      decoration: textFieldDecoration.copyWith(
        contentPadding: const EdgeInsets.symmetric(
          vertical: 13,
          horizontal: 8,
        ),
      ),
      value: selectedTokenContract,
      items: [
        DropdownMenuItem(
          value: selectedTokenContract,
          child: Text(
            GetIt.instance<WalletCubit>().currentActiveChain.symbol ?? "",
            style: const TextStyle().interStyle.copyWith(
                height: 20 / 14,
                color: white,
                fontSize: 14,
                fontWeight: FontWeight.w400),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        // ...tokens.values.map((TokenCustomModel token) => DropdownMenuItem(
        //       value: token.token.contract,
        //       child: Text(
        //         style: const TextStyle().interStyle.copyWith(
        //             height: 20 / 14,
        //             color: white,
        //             fontSize: 14,
        //             fontWeight: FontWeight.w400),
        //         token.token.symbol!,
        //         overflow: TextOverflow.ellipsis,
        //         maxLines: 1,
        //       ),
        //     ))
      ],
      onChanged: (val) {
        if (val != null) {
          //   setState(() {
          selectedTokenContract = val;
          amountTextController.text = "";
          //   });
          //   fetchGasFee();
        }
      },
    );
  }
}
