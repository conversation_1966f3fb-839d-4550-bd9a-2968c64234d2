import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:mlx_flutter/mlx_flutter.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/core/repository/chain_repository.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/core/service/auth_service.dart';
import 'package:toii_social/core/service/metamask/wallet_connector_service.dart';
import 'package:toii_social/core/service/social_service.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/utils/interceptor/log_api_intercepter.dart';
import 'package:toii_social/utils/interceptor/token_interceptor.dart';
import 'package:toii_social/utils/navigation_service.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';

GetIt serviceLocator = GetIt.instance;
Future setupLocator() async {
  await SharedPref.getInstance();

  ///Setup Navigation Service
  serviceLocator.registerLazySingleton(() => NavigationService());

  serviceLocator.registerLazySingleton(() => ThemeCubit());

  final Dio dio = await setupDio("https://api-dev.toii.social");
  serviceLocator.registerLazySingleton<Dio>(() => dio);

  serviceLocator.registerSingleton<WalletConnectorService>(
    MetamaskConnectorImpl(),
  );

  /// Service
  serviceLocator.registerLazySingleton(() => AuthService(serviceLocator()));
  serviceLocator.registerLazySingleton(() => SocialService(serviceLocator()));

  serviceLocator.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(authService: serviceLocator()),
  );

  serviceLocator.registerLazySingleton<SocialRepository>(
    () => SocialRepositoryImpl(socialService: serviceLocator()),
  );

  final mlxFlutterPlugin = MlxFlutter();
  serviceLocator.registerLazySingleton(() => mlxFlutterPlugin);

  // cubit
  serviceLocator.registerLazySingleton(() => HomeCubit());

  serviceLocator.registerLazySingleton<ChainRepository>(
    () => ChainRepositoryImpl(),
  );
  serviceLocator.registerLazySingleton(() => ProfileCubit());
}

Future<Dio> setupDio(String baseUrl) async {
  final options = BaseOptions(
    receiveDataWhenStatusError: true,
    connectTimeout: const Duration(seconds: 60),
    receiveTimeout: const Duration(seconds: 60),
    responseType: ResponseType.json,
    baseUrl: baseUrl,
  );
  final Dio dio = Dio(options);

  dio.interceptors.add(TokenInterceptor());
  dio.interceptors.add(LogAPIInterceptor());

  dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
  dio.interceptors.add(LogInterceptor(requestBody: true, responseBody: true));
  return dio;
}
