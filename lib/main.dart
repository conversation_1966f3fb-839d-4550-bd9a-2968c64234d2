import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/locator/locator.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/logger/log_api_screen.dart';
import 'package:toii_social/utils/device_id/device_id_utils.dart';
import 'package:toii_social/utils/navigation_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await setupLocator();
  await getUniqueDeviceId();
  runApp(const App());
}

class App extends StatelessWidget {
  const App({super.key});
  @override
  Widget build(BuildContext context) {
    //    return MultiBlocProvider(
    //     providers: [
    //       BlocProvider.value(value: GetIt.instance<ThemeCubit>()),
    //       BlocProvider.value(value: GetIt.instance<AuthCubit>()),
    //     ],
    //     child: BlocBuilder<ThemeCubit, ThemeState>(
    //       builder: (BuildContext ctx, state) {
    //         return BlocBuilder<AuthCubit, AuthState>(
    //             builder: (BuildContext ctx, state) {
    //           return MaterialApp.router(
    //             title: 'VPBANKS',
    //             color: Colors.white,
    //             theme: ctx.read<ThemeCubit>().themeData,
    //             debugShowCheckedModeBanner: false,
    //             locale: Locale('vi', ''),
    //             supportedLocales: const [
    //               Locale('en', ''),
    //               Locale('vi', ''),
    //             ],
    //             localizationsDelegates:
    //                 ModuleManagement().localizationsDelegates(),
    //             routerConfig: router,
    //           );
    //         });
    //       },
    //     ),
    //   );
    // }
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: GetIt.instance<ThemeCubit>()),
        //  BlocProvider.value(value: GetIt.instance<AuthCubit>()),
      ],
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (BuildContext ctx, state) {
          return LogApiConfigScreen(
            navigatorKey: GetIt.instance<NavigationService>().navigatorKey,
            child: MaterialApp.router(
              debugShowCheckedModeBanner: false,
              themeMode: ThemeMode.light,
              // theme: AppTheme.lightTheme,
              supportedLocales: const <Locale>[
                Locale.fromSubtags(languageCode: 'en'),
              ],
              //  locale: Locale(languageCode),
              //darkTheme: AppTheme.darkTheme,
              routerConfig: router,
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
            ),
          );
        },
      ),
    );
  }
}
