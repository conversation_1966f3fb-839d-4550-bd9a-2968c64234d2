part of 'login_cubit.dart';

enum LoginStatus { initial, loading, success, failure, newUser }

extension LoginStatusX on LoginStatus {
  bool get isInitial => this == LoginStatus.initial;
  bool get isLoading => this == LoginStatus.loading;
  bool get isSuccess => this == LoginStatus.success;
  bool get isFailure => this == LoginStatus.failure;
  bool get isNewUser => this == LoginStatus.newUser;
}

final class LoginState extends Equatable {
  final LoginStatus status;
  final String? message;

  const LoginState(
      {this.status = LoginStatus.initial, this.message});

  @override
  List<Object?> get props => [status,  message];

  LoginState copyWith({
    LoginStatus? status,
    String? message,
  }) {
    return LoginState(
        status: status ?? this.status,
        message: message ?? this.message);
  }
}
