import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/utils/utils/validator.dart';

part 'login_validate_state.dart';

class LoginValidateCubit extends Cubit<LoginValidateState> {
  LoginValidateCubit() : super(const LoginValidateState());

  bool validateForm({String? email, String? password}) {
    bool isValid = true;
    if (email != null) {
      if (email.isEmpty) {
        emit(state.copyWith(errorEmail: "Please input email"));
        isValid = false;
      } else if (email.isValidEmail() == false) {
        emit(state.copyWith(errorEmail: "Email invalid"));
        isValid = false;
      } else {
        emit(state.copyWith(errorEmail: ""));
      }
    }
    if (password != null) {
      if (password.isEmpty) {
        emit(state.copyWith(errorPassword: "Please input password"));
        isValid = false;
      } else {
        emit(state.copyWith(errorPassword: ""));
      }
    }
    return isValid;
  }

  void clearErrorEmail() {
    emit(state.copyWith(errorEmail: ""));
  }

  void clearErrorPassword() {
    emit(state.copyWith(errorPassword: ""));
  }

  void setErrorWhenLogin(String error) {
    emit(state.copyWith(errorPassword: error));
  }

  void toggleShowPassword() {
    emit(state.copyWith(isShowPassword: !state.isShowPassword));
  }
}
