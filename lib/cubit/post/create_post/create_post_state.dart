part of 'create_post_cubit.dart';

enum CreatePostStatus { initial, loading, success, failure }

enum ImageUploadStatus { initial, loading, success, failure }

extension CreatePostStatusX on CreatePostStatus {
  bool get isInitial => this == CreatePostStatus.initial;
  bool get isLoading => this == CreatePostStatus.loading;
  bool get isSuccess => this == CreatePostStatus.success;
  bool get isFailure => this == CreatePostStatus.failure;
}

extension ImageUploadStatusX on ImageUploadStatus {
  bool get isInitial => this == ImageUploadStatus.initial;
  bool get isLoading => this == ImageUploadStatus.loading;
  bool get isSuccess => this == ImageUploadStatus.success;
  bool get isFailure => this == ImageUploadStatus.failure;
}

class CreatePostState extends Equatable {
  final CreatePostStatus status;
  final PostModel? post;
  final String? errorMessage;
  final ImageUploadStatus imageUploadStatus;
  final List<String> selectedImagePaths;
  final List<String> uploadedMediaKeys;
  final String? imageUploadError;
  final PrivacyOption privacy;

  const CreatePostState({
    this.status = CreatePostStatus.initial,
    this.post,
    this.errorMessage,
    this.imageUploadStatus = ImageUploadStatus.initial,
    this.selectedImagePaths = const [],
    this.uploadedMediaKeys = const [],
    this.imageUploadError,
    this.privacy = PrivacyOption.everyone,
  });

  CreatePostState copyWith({
    CreatePostStatus? status,
    PostModel? post,
    String? errorMessage,
    ImageUploadStatus? imageUploadStatus,
    List<String>? selectedImagePaths,
    List<String>? uploadedMediaKeys,
    String? imageUploadError,
    PrivacyOption? privacy,
  }) {
    return CreatePostState(
      status: status ?? this.status,
      post: post ?? this.post,
      errorMessage: errorMessage ?? this.errorMessage,
      imageUploadStatus: imageUploadStatus ?? this.imageUploadStatus,
      selectedImagePaths: selectedImagePaths ?? this.selectedImagePaths,
      uploadedMediaKeys: uploadedMediaKeys ?? this.uploadedMediaKeys,
      imageUploadError: imageUploadError ?? this.imageUploadError,
      privacy: privacy ?? this.privacy,
    );
  }

  @override
  List<Object?> get props => [
    status,
    post,
    errorMessage,
    imageUploadStatus,
    selectedImagePaths,
    uploadedMediaKeys,
    imageUploadError,
    privacy,
  ];
}
