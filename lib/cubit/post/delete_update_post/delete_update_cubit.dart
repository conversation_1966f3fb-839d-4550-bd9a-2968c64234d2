import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/core/repository/social_repository.dart';

import 'delete_update_state.dart';

class DeleteUpdatePostCubit extends Cubit<DeleteUpdatePostState> {
  final SocialRepository socialRepository;

  DeleteUpdatePostCubit({required this.socialRepository})
    : super(const DeleteUpdatePostState());

  Future<void> deletePost(String id) async {
    emit(state.copyWith(status: DeleteUpdatePostStatus.loading));
    try {
      await socialRepository.deletePost(id);
      emit(
        state.copyWith(
          status: DeleteUpdatePostStatus.success,
          message: 'Post deleted successfully',
          errorMessage: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: DeleteUpdatePostStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    }
  }
}
