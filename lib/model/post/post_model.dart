import 'package:json_annotation/json_annotation.dart';
import 'package:toii_social/model/post/reaction_model.dart';

part 'post_model.g.dart';

@JsonSerializable()
class PostResponseDataModel {
  final List<PostModel> posts;
  final int total;

  PostResponseDataModel({required this.posts, required this.total});

  factory PostResponseDataModel.fromJson(Map<String, dynamic> json) =>
      _$PostResponseDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$PostResponseDataModelToJson(this);
}

@JsonSerializable()
class PostModel {
  const PostModel({
    required this.id,
    this.user,
    required this.content,
    this.mediaKeys = const [],
    this.mediaUrls = const [],

    required this.reposts,
    required this.comments,
    this.reactions,
    this.privacy,
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final PostUserModel? user;
  @<PERSON><PERSON><PERSON>ey(defaultValue: "")
  final String content;

  @Json<PERSON>ey(name: 'media_keys')
  final List<String> mediaKeys;

  @JsonKey(name: 'media_urls')
  final List<String> mediaUrls;

  @JsonKey(defaultValue: 0)
  final int reposts;
  @JsonKey(defaultValue: 0)
  final int comments;
  final ReactionGroupModel? reactions;
  final String? privacy;

  @JsonKey(name: 'created_at')
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  factory PostModel.fromJson(Map<String, dynamic> json) =>
      _$PostModelFromJson(json);

  Map<String, dynamic> toJson() => _$PostModelToJson(this);

  PostModel copyWith({
    String? id,
    PostUserModel? user,
    String? content,
    List<String>? mediaKeys,
    List<String>? mediaUrls,
    int? reposts,
    int? comments,
    ReactionGroupModel? reactions,
  }) {
    return PostModel(
      id: id ?? this.id,
      user: user,
      content: content ?? this.content,
      mediaKeys: mediaKeys ?? this.mediaKeys,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      reposts: reposts ?? this.reposts,
      comments: comments ?? this.comments,
      reactions: reactions ?? this.reactions,
    );
  }

  int get actionlikes {
    if (reactions == null) return 0;
    // todo sửa enum action sau
    return reactions!.reactions
            ?.where((reaction) => reaction.type?.toLowerCase() == 'love')
            .firstOrNull
            ?.count ??
        0;
  }
}

extension PostModelExtension on PostModel {
  bool get isLiked {
    if (reactions == null) return false;
    return reactions!.userReactions?.contains('love') ?? false;
    // todo sửa enum action sau
  }

  String get getViewLike {
    if (actionlikes == 0) return "Like";
    if (actionlikes >= 1000)
      return "${(actionlikes / 1000).toStringAsFixed(1)}K";
    return actionlikes.toString();
  }

  String get getViewComment {
    if (comments == 0) return "Reply";
    if (comments >= 1000) return "${(comments / 1000).toStringAsFixed(1)}K";
    return comments.toString();
  }

  String get getViewRepost {
    if (reposts == 0) return "Share";
    if (reposts >= 1000) return "${(reposts / 1000).toStringAsFixed(1)}K";
    return reposts.toString();
  }
}

@JsonSerializable()
class PostUserModel {
  const PostUserModel({
    required this.id,
    this.username,
    this.phoneNumber,
    this.email,
    this.firstName,
    this.lastName,
    this.fullName,
    this.avatar,
    this.address,
    this.moreInfo = const {},
    this.lastActivity,
    this.role,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  final String id;
  final String? username;

  @JsonKey(name: 'phoneNumber')
  final String? phoneNumber;

  final String? email;

  @JsonKey(name: 'first_name')
  final String? firstName;

  @JsonKey(name: 'last_name')
  final String? lastName;

  @JsonKey(name: 'full_name')
  final String? fullName;

  final String? avatar;
  final String? address;

  @JsonKey(name: 'more_info')
  final Map<String, dynamic> moreInfo;

  @JsonKey(name: 'last_activity')
  final String? lastActivity;

  final String? role;
  final String? status;

  @JsonKey(name: 'created_at')
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  @JsonKey(name: 'deleted_at')
  final String? deletedAt;

  factory PostUserModel.fromJson(Map<String, dynamic> json) =>
      _$PostUserModelFromJson(json);

  Map<String, dynamic> toJson() => _$PostUserModelToJson(this);
}

@JsonSerializable()
class CreatePostRequestModel {
  const CreatePostRequestModel({
    required this.content,
    required this.privacy,
    this.mediaKeys = const [],
  });
  final String content;

  @JsonKey(name: 'media_keys')
  final List<String> mediaKeys;

  final String privacy;

  factory CreatePostRequestModel.fromJson(Map<String, dynamic> json) =>
      _$CreatePostRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreatePostRequestModelToJson(this);
}
