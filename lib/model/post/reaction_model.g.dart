// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reaction_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReactionGroupModel _$ReactionGroupModelFromJson(Map<String, dynamic> json) =>
    ReactionGroupModel(
      totalReactions: (json['total_reactions'] as num?)?.toInt(),
      reactions:
          (json['reactions'] as List<dynamic>?)
              ?.map((e) => ReactionModel.fromJson(e as Map<String, dynamic>))
              .toList(),
      userReaction: json['user_reaction'] as String?,
      userReactions:
          (json['user_reactions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
    );

Map<String, dynamic> _$ReactionGroupModelToJson(ReactionGroupModel instance) =>
    <String, dynamic>{
      'total_reactions': instance.totalReactions,
      'reactions': instance.reactions,
      'user_reaction': instance.userReaction,
      'user_reactions': instance.userReactions,
    };

ReactionModel _$ReactionModelFromJson(Map<String, dynamic> json) =>
    ReactionModel(
      type: json['type'] as String?,
      emoji: json['emoji'] as String?,
      name: json['name'] as String?,
      count: (json['count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ReactionModelToJson(ReactionModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'emoji': instance.emoji,
      'name': instance.name,
      'count': instance.count,
    };
