import 'package:json_annotation/json_annotation.dart';

import 'token_model.dart';


part 'chain_model.g.dart';

@JsonSerializable()
class ChainModel {
  String? name;
  String? rpcUrl;
  String? rpc;
  String? chainId;
  String? symbol;
  double balance = 0;
  String? explorer;
  bool? isDefault;
  String? iconUrl;

  List<TokenModel> tokens = [];

  ChainModel(
      {this.name,
      this.rpcUrl,
      this.chainId,
      this.symbol,
      this.balance = 0,
      this.tokens = const [],
      this.iconUrl,
      this.isDefault});
  factory ChainModel.fromJson(Map<String, dynamic> json) =>
      _$ChainModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChainModelToJson(this);

  ChainModel copyWith({
    String? name,
    String? rpcUrl,
    String? chainId,
    String? symbol,
    double? balance,
    List<TokenModel>? tokens,
    String? iconUrl,
  }) {
    return ChainModel(
      name: name ?? this.name,
      rpcUrl: rpcUrl ?? this.rpcUrl,
      chainId: chainId ?? this.chainId,
      symbol: symbol ?? this.symbol,
      balance: balance ?? this.balance,
      tokens: tokens ?? this.tokens,
      iconUrl: iconUrl ?? this.iconUrl,
    );
  }
}
