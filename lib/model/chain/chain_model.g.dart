// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chain_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChainModel _$ChainModelFromJson(Map<String, dynamic> json) =>
    ChainModel(
        name: json['name'] as String?,
        rpcUrl: json['rpcUrl'] as String?,
        chainId: json['chainId'] as String?,
        symbol: json['symbol'] as String?,
        balance: (json['balance'] as num?)?.toDouble() ?? 0,
        tokens:
            (json['tokens'] as List<dynamic>?)
                ?.map((e) => TokenModel.fromJson(e as Map<String, dynamic>))
                .toList() ??
            const [],
        iconUrl: json['iconUrl'] as String?,
        isDefault: json['isDefault'] as bool?,
      )
      ..rpc = json['rpc'] as String?
      ..explorer = json['explorer'] as String?;

Map<String, dynamic> _$ChainModelToJson(ChainModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'rpcUrl': instance.rpcUrl,
      'rpc': instance.rpc,
      'chainId': instance.chainId,
      'symbol': instance.symbol,
      'balance': instance.balance,
      'explorer': instance.explorer,
      'isDefault': instance.isDefault,
      'iconUrl': instance.iconUrl,
      'tokens': instance.tokens,
    };
