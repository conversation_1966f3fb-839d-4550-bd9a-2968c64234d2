// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MediaUploadResponseModel _$MediaUploadResponseModelFromJson(
  Map<String, dynamic> json,
) => MediaUploadResponseModel(
  id: json['id'] as String?,
  userId: json['user_id'] as String?,
  type: json['type'] as String?,
  fileName: json['file_name'] as String?,
  bucket: json['bucket'] as String?,
  key: json['key'] as String?,
  s3Url: json['s3_url'] as String?,
  cdnUrl: json['cdn_url'] as String?,
  size: (json['size'] as num?)?.toInt(),
  contentType: json['content_type'] as String?,
  createdAt: json['created_at'] as String?,
  updatedAt: json['updated_at'] as String?,
);

Map<String, dynamic> _$MediaUploadResponseModelToJson(
  MediaUploadResponseModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'user_id': instance.userId,
  'type': instance.type,
  'file_name': instance.fileName,
  'bucket': instance.bucket,
  'key': instance.key,
  's3_url': instance.s3Url,
  'cdn_url': instance.cdnUrl,
  'size': instance.size,
  'content_type': instance.contentType,
  'created_at': instance.createdAt,
  'updated_at': instance.updatedAt,
};

MediaUploadRequestModel _$MediaUploadRequestModelFromJson(
  Map<String, dynamic> json,
) => MediaUploadRequestModel(type: json['type'] as String);

Map<String, dynamic> _$MediaUploadRequestModelToJson(
  MediaUploadRequestModel instance,
) => <String, dynamic>{'type': instance.type};
