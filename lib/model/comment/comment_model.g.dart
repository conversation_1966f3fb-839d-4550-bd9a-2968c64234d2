// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'comment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommentListModel _$CommentListModelFromJson(Map<String, dynamic> json) =>
    CommentListModel(
      comments:
          (json['comments'] as List<dynamic>?)
              ?.map((e) => CommentItemModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$CommentListModelToJson(CommentListModel instance) =>
    <String, dynamic>{
      'comments': instance.comments.map((e) => e.toJson()).toList(),
      'total': instance.total,
    };

CommentItemModel _$CommentItemModelFromJson(Map<String, dynamic> json) =>
    CommentItemModel(
      id: json['id'] as String? ?? '',
      postId: json['post_id'] as String?,
      user:
          json['user'] == null
              ? null
              : PostUserModel.fromJson(json['user'] as Map<String, dynamic>),
      content: json['content'] as String? ?? '',
      likes: (json['likes'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      reactions:
          json['reactions'] == null
              ? null
              : ReactionGroupModel.fromJson(
                json['reactions'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$CommentItemModelToJson(CommentItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'post_id': instance.postId,
      'user': instance.user,
      'content': instance.content,
      'likes': instance.likes,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'reactions': instance.reactions,
    };

CreateCommentRequestModel _$CreateCommentRequestModelFromJson(
  Map<String, dynamic> json,
) => CreateCommentRequestModel(
  content: json['content'] as String,
  mediaKeys:
      (json['media_keys'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$CreateCommentRequestModelToJson(
  CreateCommentRequestModel instance,
) => <String, dynamic>{
  'content': instance.content,
  'media_keys': instance.mediaKeys,
};
