// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_gmail_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginGmailRequestModel _$LoginGmailRequestModelFromJson(
  Map<String, dynamic> json,
) => LoginGmailRequestModel(
  accessToken: json['access_token'] as String,
  idToken: json['id_token'] as String,
  appType: json['appType'] as String? ?? "toii-social",
);

Map<String, dynamic> _$LoginGmailRequestModelToJson(
  LoginGmailRequestModel instance,
) => <String, dynamic>{
  'appType': instance.appType,
  'access_token': instance.accessToken,
  'id_token': instance.idToken,
};

LoginAppleRequestModel _$LoginAppleRequestModelFromJson(
  Map<String, dynamic> json,
) => LoginAppleRequestModel(
  idToken: json['id_token'] as String,
  appType: json['appType'] as String? ?? "toii-social",
);

Map<String, dynamic> _$LoginAppleRequestModelToJson(
  LoginAppleRequestModel instance,
) => <String, dynamic>{
  'appType': instance.appType,
  'id_token': instance.idToken,
};
