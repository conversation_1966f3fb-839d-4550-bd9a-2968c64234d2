// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_otp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RequestOtpModel _$RequestOtpModelFromJson(Map<String, dynamic> json) =>
    RequestOtpModel(
      email: json['email'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      purpose: json['purpose'] as String,
      appType: json['appType'] as String? ?? "toii-social",
    );

Map<String, dynamic> _$RequestOtpModelToJson(RequestOtpModel instance) =>
    <String, dynamic>{
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'purpose': instance.purpose,
      'appType': instance.appType,
    };

RequestVerifyOtpModel _$RequestVerifyOtpModelFromJson(
  Map<String, dynamic> json,
) => RequestVerifyOtpModel(
  email: json['email'] as String?,
  phoneNumber: json['phoneNumber'] as String?,
  purpose: json['purpose'] as String,
  otp: json['otp'] as String,
  appType: json['appType'] as String? ?? "toii-social",
);

Map<String, dynamic> _$RequestVerifyOtpModelToJson(
  RequestVerifyOtpModel instance,
) => <String, dynamic>{
  'email': instance.email,
  'phoneNumber': instance.phoneNumber,
  'purpose': instance.purpose,
  'otp': instance.otp,
  'appType': instance.appType,
};
