import 'package:toii_social/model/chain/chain_model.dart';

final List<ChainModel> DEFAULT_CHAINS = [
   ChainModel(
    name: "TOII Testnet",
    rpcUrl:
        "https://toii-testnet.rpc.caldera.xyz/http",
    symbol: "Toii",
    tokens: [],
    chainId: "472025",
    isDefault: true,
  ),
  ChainModel(
    name: "Base ankr",
    rpcUrl:
        "https://rpc.ankr.com/base_sepolia/dc3359a3d6c4f6866d0e59e41b886d8806cba7197232edf7412c79644595b948",
    symbol: "ETH",
    tokens: [],
    chainId: "84532",
  ),
  ChainModel(
    name: "Ethereum",
    rpcUrl: "https://eth.llamarpc.com",
    symbol: "ETH",
    tokens: [],
    chainId: "1",
  ),
  ChainModel(
    name: "Atela network",
    rpcUrl: "https://testnet-rpc.atleta.network:9944",
    symbol: "ATLA",
    tokens: [],
    chainId: "2340",
  ),
  ChainModel(
    name: "Arbitrum One",
    rpcUrl: "https://arb-mainnet-public.unifra.io",
    symbol: "ETH",
    tokens: [],
    chainId: "42161",
  ),
  ChainModel(
    name: "zkSync Era",
    rpcUrl: "https://mainnet.era.zksync.io",
    symbol: "ETH",
    tokens: [],
    chainId: "324",
  ),
  ChainModel(
    name: "Binance Smart Chain",
    rpcUrl: "https://bsc-dataseed4.defibit.io",
    symbol: "BNB",
    tokens: [],
    chainId: "56",
  ),
  ChainModel(
    name: "Polygon",
    rpcUrl: "https://polygon-bor.publicnode.com",
    symbol: "MATIC",
    tokens: [],
    chainId: "137",
  ),
  ChainModel(
    name: "Mumbai Testnet",
    rpcUrl: "https://polygon-mumbai.blockpi.network/v1/rpc/public",
    symbol: "MATIC",
    tokens: [],
    chainId: "80001",
  ),
];