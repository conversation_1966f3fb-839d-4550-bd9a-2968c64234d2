import 'dart:convert';

import 'package:get_it/get_it.dart';
import 'package:tangem_flutter/core/api/chain_api_service.dart';
import 'package:tangem_flutter/core/utils/shared_preferences_manager.dart';
import 'package:tangem_flutter/cubit/wallet/wallet_cubit.dart';
import 'package:tangem_flutter/models/chain/chain_model.dart';
import 'package:tangem_flutter/models/token_item_model.dart';

abstract class ChainApiRepository {
  Future<List<ChainModel>> getAllChain();
  Future<List<TokenItemModel>> getTokenByChain(
    String chainId,
  );
  Future<List<TokenItemModel>> getTokenLocalByChain(
    String chainId,
  );
  void setTokenNewToLocal(
    List<TokenItemModel> listsAdd,
    String chainId,
  );
  void addNewCustomToken(
    TokenItemModel token,
  );
}

class ChainApiRepositoryImpl implements ChainApiRepository {
  final tokenChainKey = "KEY_TOKENCHAIN";
  final ChainApiService chainApiService;
  ChainApiRepositoryImpl({required this.chainApiService});

  @override
  Future<List<ChainModel>> getAllChain() async {
    // TODO: implement getAllChain
    return await chainApiService.getAllChain();
  }

  @override
  Future<List<TokenItemModel>> getTokenByChain(
    String chainId,
  ) async {
    return await chainApiService.getTokenByChain(chainId);
  }

  @override
  Future<List<TokenItemModel>> getTokenLocalByChain(
    String chainId,
  ) async {
    final listTokenStr =
        GetIt.instance<SharedPreferencesManager>().getString(tokenChainKey) ??
            "";
    Map<String, dynamic> tokenJson =
        listTokenStr.isEmpty ? {} : json.decode(listTokenStr);
    if (tokenJson.isEmpty || tokenJson[chainId] == null) {
      return [];
    }
    List<TokenItemModel> lists = [];

    final List<dynamic> jsonToken = tokenJson[chainId] ?? [];
    for (var element in jsonToken) {
      final token = TokenItemModel.fromJson(element);
      if (token.isHidden == false) {
        lists.add(token);
      }
    }

    return lists;
  }

  @override
  void setTokenNewToLocal(
    List<TokenItemModel> listsAdd,
    String chainId,
  ) {
    final listTokenStr =
        GetIt.instance<SharedPreferencesManager>().getString(tokenChainKey) ??
            "";
    Map<String, dynamic> tokenJson =
        listTokenStr.isEmpty ? {} : json.decode(listTokenStr);
    final List<dynamic> jsonToken = tokenJson[chainId] ?? [];
    for (var token in listsAdd) {
      bool isAdded = false;
      for (var element in jsonToken) {
        final token = TokenItemModel.fromJson(element);
        if (token.address == token.address) {
          isAdded = true;
          break;
        }
      }
      if (!isAdded) {
        jsonToken.add(token.toJson());
      }
    }
    tokenJson[chainId] = jsonToken;
    GetIt.instance<SharedPreferencesManager>()
        .putString(tokenChainKey, json.encode(tokenJson));
  }

  @override
  void addNewCustomToken(
    TokenItemModel token,
  ) {
    final listTokenStr =
        GetIt.instance<SharedPreferencesManager>().getString(tokenChainKey) ??
            "";
    Map<String, dynamic> tokenJson =
        listTokenStr.isEmpty ? {} : json.decode(listTokenStr);
    final currentActiveChain = GetIt.instance<WalletCubit>().currentActiveChain;

    final List<dynamic> jsonToken = tokenJson[currentActiveChain.chainId] ?? [];

    tokenJson[currentActiveChain.chainId!] = jsonToken;
    bool isAdded = false;
    for (var element in jsonToken) {
      final token1 = TokenItemModel.fromJson(element);
      if (token1.address == token.address) {
        isAdded = true;
        break;
      }
    }
    if (!isAdded) {
      jsonToken.add(token.toJson());
      tokenJson[currentActiveChain.chainId!] = jsonToken;
      GetIt.instance<SharedPreferencesManager>()
          .putString(tokenChainKey, json.encode(tokenJson));
    }
  }
}
