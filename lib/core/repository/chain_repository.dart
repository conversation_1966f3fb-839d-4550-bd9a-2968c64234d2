import 'dart:async';
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:hex/hex.dart';
import 'package:http/http.dart';
// import 'package:tangem_flutter/core/utils/eth_amount_formatter.dart';
// import 'package:tangem_flutter/cubit/transactions/transaction_details_cubit.dart';

// import 'package:tangem_flutter/models/transaction/transaction_model.dart';
import 'package:toii_social/model/chain/chain_model.dart';
import 'package:toii_social/model/chain/token_model.dart';
import 'package:web3dart/crypto.dart';
import 'package:web3dart/web3dart.dart';

enum WalletTransferType { ether, token }

typedef TransferEvent = void Function(EthereumAddress from, EthereumAddress to,
    BigInt value, String? transactionId);

abstract class ChainRepository {
  Future<double> fetchBalanceFromChain(String address, ChainModel chain);
  Future<double> fetchBalanceOfToken(
    String publicAddress,
    String contractAddressHex,
    ChainModel chain,
  );
  // Future<EtherAmount> fetchGasFee(String privateAddress, ChainModel chain);
  // Future<EtherAmount> fetchEstimateGasFee({
  //   required String receipentPublicAddress,
  //   required chain,
  // });
  // Future<EtherAmount> fetchEstimateTokenTransferGasFee({
  //   required String receipentPublicAddress,
  //   required String senderPublicAddress,
  //   required String contractAddress,
  //   required ChainModel chain,
  //   required EtherAmount value,
  // });

  // Future<String?> sendTransaction({
  //   String? contractAddressHex,
  //   required String privateKey,
  //   required WalletTransferType type,
  //   required EtherAmount amount,
  //   required ChainModel chain,
  //   required String receiver,
  //   TransferEvent? onTransfer,
  //   Function(Object exeception)? onError,
  // });
  // Future<String> transferToken({
  //   required String receipentPublicAddress,
  //   required ChainModel chain,
  //   required String contractAddressHex,
  //   required EtherAmount amount,
  //   required String senderPublicAddress,
  //   required String senderPrivateKey,
  //   required int senderChainId,
  // });
  Future<TokenModel> fetchTokenInfo(
    String contractAddressHex,
    ChainModel chain,
  );

  // Future<TransactionModel?> fetchTransactionDetails(
  //   String transactionId,
  //   ChainModel chain,
  // );
}

class ChainRepositoryImpl implements ChainRepository {
  ChainRepositoryImpl();

  @override
  Future<TokenModel> fetchTokenInfo(
    String contractAddressHex,
    ChainModel chain,
  ) async {
    // Setup Client
    var httpClient = Client();
    var ethClient = Web3Client(chain.rpcUrl!, httpClient);

    // Setup Credential
    final EthereumAddress contractAddress =
        EthereumAddress.fromHex(contractAddressHex);

    // Setup Contract
    String abiCode =
        await rootBundle.loadString('assets/abi/erc20tokenabi.json');
    final contract = DeployedContract(
      ContractAbi.fromJson(abiCode, 'Erc20Token'),
      contractAddress,
    );
    final nameFunction = contract.function('name');
    final symbolFunction = contract.function('symbol');
    final decimalsFunction = contract.function('decimals');

    final name = await ethClient
        .call(contract: contract, function: nameFunction, params: []);
    final decimal = await ethClient
        .call(contract: contract, function: decimalsFunction, params: []);
    final symbol = await ethClient
        .call(contract: contract, function: symbolFunction, params: []);
    TokenModel token = TokenModel(
        name: name[0].toString(),
        contract: contractAddressHex,
        symbol: symbol[0].toString(),
        decimal: decimal[0].toString());
    return token;
  }

  @override
  Future<double> fetchBalanceFromChain(String address, ChainModel chain) async {
    var httpClient = Client();
    final rpcUrl = chain.rpcUrl ?? chain.rpc;
    log("Getting ${chain.symbol} Balance FROM $address on ${chain.name} CHAIN ($rpcUrl)");

    var ethClient = Web3Client(rpcUrl!, httpClient);
    EthereumAddress ethereumAddress = EthereumAddress.fromHex(address);
    EtherAmount balance = await ethClient.getBalance(ethereumAddress);

    return balance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<double> fetchBalanceOfToken(
    String publicAddress,
    String contractAddressHex,
    ChainModel chain,
  ) async {
    try {
      // Setup Client
      var httpClient = Client();
      var ethClient = Web3Client(chain.rpcUrl!, httpClient);

      // Setup Credential
      final EthereumAddress contractAddress =
          EthereumAddress.fromHex(contractAddressHex);
      final EthereumAddress address = EthereumAddress.fromHex(publicAddress);

      // Setup Contract
      String abiCode =
          await rootBundle.loadString('assets/abi/erc20tokenabi.json');
      final contract = DeployedContract(
        ContractAbi.fromJson(abiCode, 'Erc20Token'),
        contractAddress,
      );
      final balanceFunction = contract.function('balanceOf');

      final balance = await ethClient.call(
          contract: contract, function: balanceFunction, params: [address]);
      return balance[0];
      //  return EthAmountFormatter(balance[0]).format();
    } catch (e) {
      return 0.0;
    }
  }

  // @override
  // Future<EtherAmount> fetchGasFee(
  //     String privateAddress, ChainModel chain) async {
  //   var httpClient = Client();
  //   var ethClient = Web3Client(chain.rpcUrl!, httpClient);

  //   EtherAmount gasPrice = await ethClient.getGasPrice();
  //   return gasPrice;
  // }

  // @override
  // Future<EtherAmount> fetchEstimateGasFee({
  //   required String receipentPublicAddress,
  //   required chain,
  // }) async {
  //   var httpClient = Client();
  //   var ethClient = Web3Client(chain.rpcUrl!, httpClient);

  //   // Gas Amount
  //   BigInt estimatedGasAmount = await ethClient.estimateGas(
  //     sender:
  //         EthereumAddress.fromHex("******************************************"),
  //     to: EthereumAddress.fromHex(receipentPublicAddress),
  //   );

  //   // Gas Price
  //   EtherAmount gasPrice = await ethClient.getGasPrice();

  //   // Gas Fee
  //   EtherAmount estimatedGasFee = EtherAmount.inWei(
  //     BigInt.from(gasPrice.getValueInUnit(EtherUnit.wei)) * estimatedGasAmount,
  //   );

  //   return estimatedGasFee;
  // }

  // @override
  // Future<EtherAmount> fetchEstimateTokenTransferGasFee({
  //   required String receipentPublicAddress,
  //   required String senderPublicAddress,
  //   required String contractAddress,
  //   required ChainModel chain,
  //   required EtherAmount value,
  // }) async {
  //   // Setup Client
  //   var httpClient = Client();
  //   var ethClient = Web3Client(chain.rpcUrl!, httpClient);

  //   // Contract Data
  //   final transferFunction = ContractFunction(
  //     'transfer',
  //     [
  //       FunctionParameter("_to", parseAbiType("address")),
  //       FunctionParameter("_value", parseAbiType("uint256")),
  //     ],
  //   );
  //   final data = transferFunction.encodeCall([
  //     EthereumAddress.fromHex(receipentPublicAddress),
  //     value.getInWei,
  //   ]);

  //   // Gas Amount
  //   BigInt estimatedGasAmount = await ethClient.estimateGas(
  //     sender: EthereumAddress.fromHex(senderPublicAddress),
  //     to: EthereumAddress.fromHex(contractAddress),
  //     data: data,
  //   );

  //   // Gas Price
  //   EtherAmount gasPrice = await ethClient.getGasPrice();

  //   // Gas Fee
  //   EtherAmount estimatedGasFee = EtherAmount.inWei(
  //     BigInt.from(gasPrice.getValueInUnit(EtherUnit.wei)) * estimatedGasAmount,
  //   );

  //   return estimatedGasFee;
  // }

  // @override
  // Future<String?> sendTransaction({
  //   String? contractAddressHex,
  //   required String privateKey,
  //   required WalletTransferType type,
  //   required EtherAmount amount,
  //   required ChainModel chain,
  //   required String receiver,
  //   TransferEvent? onTransfer,
  //   Function(Object exeception)? onError,
  // }) async {
  //   var httpClient = Client();
  //   var ethClient = Web3Client(chain.rpcUrl!, httpClient);
  //   final credentials = EthPrivateKey.fromHex(privateKey);
  //   final receiverAddress = EthereumAddress.fromHex(receiver);
  //   EtherAmount gasPrice = await ethClient.getGasPrice();
  //   final from = credentials.address;
  //   final Transaction transaction;
  //   // Contract Data
  //   if (type == WalletTransferType.ether) {
  //     transaction = Transaction(
  //       from: from,
  //       to: receiverAddress,
  //       gasPrice: gasPrice,
  //       value: amount,
  //     );
  //   } else {
  //     final EthereumAddress contractAddress =
  //         EthereumAddress.fromHex(contractAddressHex!);
  //     final transferFunction = ContractFunction(
  //       'transfer',
  //       [
  //         FunctionParameter("_to", parseAbiType("address")),
  //         FunctionParameter("_value", parseAbiType("uint256")),
  //       ],
  //     );
  //     final data = transferFunction.encodeCall([
  //       receiverAddress,
  //       amount.getInWei,
  //     ]);
  //     transaction = Transaction(
  //       from: from,
  //       to: contractAddress,
  //       value: EtherAmount.zero(), // For token transfers, the value is zero
  //       data: data,
  //     );
  //   }

  //   try {
  //     final transactionId = await ethClient.sendTransaction(
  //       EthPrivateKey.fromHex("0x$privateKey"),
  //       transaction,
  //       chainId: int.parse(chain.chainId!),
  //     );

  //     // pooling the transaction receipt every x seconds.
  //     Timer.periodic(const Duration(seconds: 2), (timer) async {
  //       final receipt = await ethClient.getTransactionReceipt(transactionId);

  //       if (receipt?.status ?? false) {
  //         if (onTransfer != null) {
  //           GetIt.instance<TransactionDetailsCubit>()
  //               .getTransactionDetails(transactionId: transactionId);
  //           onTransfer(from, receiverAddress, amount.getInEther, transactionId);
  //         }

  //         timer.cancel();
  //       }
  //     });

  //     log('transact started $transactionId');

  //     return transactionId;
  //   } catch (ex) {
  //     if (onError != null) {
  //       onError(ex);
  //     }
  //     return null;
  //   }
  // }

  // @override
  // Future<String> transferToken({
  //   required String receipentPublicAddress,
  //   required ChainModel chain,
  //   required String contractAddressHex,
  //   required EtherAmount amount,
  //   required String senderPublicAddress,
  //   required String senderPrivateKey,
  //   required int senderChainId,
  // }) async {
  //   // Setup Client
  //   var httpClient = Client();
  //   var ethClient = Web3Client(chain.rpcUrl!, httpClient);

  //   // Setup Credential
  //   final EthereumAddress contractAddress =
  //       EthereumAddress.fromHex(contractAddressHex);
  //   final EthereumAddress recipentAddress =
  //       EthereumAddress.fromHex(receipentPublicAddress);
  //   final EthereumAddress senderAddress =
  //       EthereumAddress.fromHex(senderPublicAddress);
  //   EthPrivateKey privateKey = EthPrivateKey.fromHex(
  //     '0x$senderPrivateKey',
  //   );

  //   // Contract Data
  //   final transferFunction = ContractFunction(
  //     'transfer',
  //     [
  //       FunctionParameter("_to", parseAbiType("address")),
  //       FunctionParameter("_value", parseAbiType("uint256")),
  //     ],
  //   );
  //   final data = transferFunction.encodeCall([
  //     recipentAddress,
  //     amount.getInWei,
  //   ]);

  //   final transaction = Transaction(
  //     from: senderAddress,
  //     to: contractAddress,
  //     value: EtherAmount.zero(), // For token transfers, the value is zero
  //     data: data,
  //   );

  //   String result = await ethClient.sendTransaction(privateKey, transaction,
  //       chainId: senderChainId);

  //   return result;
  // }

  // @override
  // Future<TransactionModel?> fetchTransactionDetails(
  //   String transactionId,
  //   ChainModel chain,
  // ) async {
  //   var httpClient = Client();
  //   var ethClient = Web3Client(chain.rpcUrl!, httpClient);

  //   final txReceipt = await ethClient.getTransactionReceipt(transactionId);
  //   final transactionInformation =
  //       await ethClient.getTransactionByHash(transactionId);
  //   bytesToHex(transactionInformation!.input);
  //   if (txReceipt != null) {
  //     final blockNumber = txReceipt.blockNumber.blockNum;
  //     final blockNumberHex = '0x${blockNumber.toRadixString(16)}';
  //     final blockInformation =
  //         await ethClient.getBlockInformation(blockNumber: blockNumberHex);
  //     final blockTimestamp = blockInformation.timestamp;
  //     final walletCurrent = GetIt.instance<WalletRepository>().walletCurrent();

  //     final publicAddress = walletCurrent?.publicAddress ?? '';

  //     // TokenModel token = await GetIt.instance<ChainRepository>().fetchTokenInfo(
  //     //     txReceipt.to!.hex, GetIt.instance<WalletCubit>().currentActiveChain);
  //     final transaction =
  //         TransactionModel.fromTransactionReceipt(txReceipt, publicAddress);
  //     transaction.timeStamp = blockTimestamp;
  //     transaction.amount = EthAmountFormatter(BigInt.parse(
  //             HEX.encode(transactionInformation!.input.sublist(36, 68)),
  //             radix: 16))
  //         .format();

  //     return transaction;
  //   }

  //   return null;
  // }

  // Map<String, dynamic> parseTokenTransferInputData(Uint8List input) {
  //   return {
  //     "toAddress":
  //         EthereumAddress.fromHex(HEX.encode(input.sublist(16, 36))).hexEip55,
  //     "amount": BigInt.parse(HEX.encode(input.sublist(36, 68)), radix: 16)
  //   };
  // }

  String uint8ListToString(
    Uint8List value, {
    bool include0x = false,
    int? forcePadLength,
    bool padToEvenLength = false,
  }) {
    return bytesToHex(value,
        include0x: include0x,
        forcePadLength: forcePadLength,
        padToEvenLength: padToEvenLength);
  }
}
