import 'package:bip32/bip32.dart' as bip32;
import 'package:bip39/bip39.dart' as bip39;
import 'package:encrypt/encrypt.dart';
import 'package:hex/hex.dart';
import 'package:toii_social/model/credentials/credentials.dart';
import 'package:web3dart/web3dart.dart' as web3;

class WalletService {
  static String generateMnemonic() {
    String mnemonic = bip39.generateMnemonic(strength: 256);
    return mnemonic;
  }

  static Future<String> generatePrivateKey(String mnemonic) async {
    final seed = bip39.mnemonicToSeed(mnemonic);
    final node = bip32.BIP32.fromSeed(seed);
    final child = node.derivePath("m/44'/60'/0'/0/0");
    final String privateKey = HEX.encode(child.privateKey!);
    return privateKey;
  }

  static String generatePublicKey(String privateKey) {
    web3.EthPrivateKey credentials = web3.EthPrivateKey.fromHex(privateKey);
    return credentials.address.hex;
  }

  static Future<Credentials> addWallet(String mnemonic) async {
    final privateKey = await generatePrivateKey(mnemonic);
    final walletAddress = generatePublicKey(privateKey);
    return Credentials(privateKeyHex: privateKey, address: walletAddress);
  }

  static Future<Credentials> createWallet() async {
    String mnemonic = generateMnemonic();
    return await addWallet(mnemonic);
  }

  static String enCryptingData({
    required String plainText,
    required String keyEncrypt,
  }) {
    final key = Key.fromUtf8(keyEncrypt.padRight(32, '0').substring(0, 32));
    final iv = IV.fromSecureRandom(16);
    final encrypter = Encrypter(AES(key));

    final encrypted = encrypter.encrypt(plainText, iv: iv);

    // Return IV and encrypted data in one string
    return '${iv.base64}:${encrypted.base64}';
  }

  static String decryptData({
    required String plainText,
    required String keyEncrypt,
  }) {
    try {
      final parts = plainText.split(':');
      if (parts.length != 2) return '';

      final iv = IV.fromBase64(parts[0]);
      final encrypted = parts[1];

      final key = Key.fromUtf8(keyEncrypt.padRight(32, '0').substring(0, 32));
      final encrypter = Encrypter(AES(key));

      return encrypter.decrypt64(encrypted, iv: iv);
    } catch (e) {
      return '';
    }
  }
}
