import 'package:flutter/foundation.dart';
import 'package:toii_social/core/service/metamask/chain_metadata.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:walletconnect_flutter_v2/apis/sign_api/models/session_models.dart';
import 'package:walletconnect_flutter_v2/apis/sign_api/models/sign_client_models.dart';
import 'package:walletconnect_flutter_v2/apis/sign_api/sign_client.dart';
import 'package:walletconnect_flutter_v2/apis/core/pairing/utils/pairing_models.dart';
import 'package:walletconnect_flutter_v2/apis/sign_api/models/json_rpc_models.dart';
import 'package:walletconnect_flutter_v2/apis/sign_api/models/proposal_models.dart';
import 'package:walletconnect_flutter_v2/apis/sign_api/models/session_models.dart';
import 'package:walletconnect_flutter_v2/apis/sign_api/models/sign_client_models.dart';
import 'package:walletconnect_flutter_v2/apis/sign_api/sign_client.dart';
import 'package:walletconnect_flutter_v2/apis/utils/errors.dart';


class WalletConstants {
  static const mainChainMetaData = ChainMetadata(
    type: "eip155",
    chainId: 'eip155:1',
    name: 'Ethereum',
    method: "personal_sign",
    events: ["chainChanged", "accountsChanged"],
    relayUrl: "wss://relay.walletconnect.com",
    projectId: "68ccdce69aec001e3cd0b33aec530b81",
    redirectUrl: "metamask://com.example.metamask_login_blog",
    walletConnectUrl: "https://walletconnect.com",
  );
  static const deepLinkMetamask = "metamask://wc?uri=";
}


abstract class WalletConnectorService {
  SignClient get wClient;
  Future<bool> initialize();
  Future<ConnectResponse?> connect();
  Future<SessionData?> authorize(ConnectResponse resp, String unSignedMessage);
  Future<String?> sendMessageForSigned(
    ConnectResponse resp,
    String walletAddress,
    String topic,
    String unSignedMessage,
  );
  Future<bool> onDisplayUri(Uri? uri);
  Future<void> disconnectWallet({required String topic});
}

class MetamaskConnectorImpl implements WalletConnectorService {
  late SignClient _wcClient;
  final ChainMetadata _chainMetadata = WalletConstants.mainChainMetaData;
  @override
  SignClient get wClient => _wcClient;

  @override
  Future<bool> initialize() async {
    bool isInitialize = false;
    try {
      _wcClient = await SignClient.createInstance(
        relayUrl: _chainMetadata.relayUrl,
        projectId: _chainMetadata.projectId,
        metadata: PairingMetadata(
          name: "MetaMask",
          description: "MetaMask login",
          url: _chainMetadata.walletConnectUrl,
          icons: ["https://wagmi.sh/icon.png"],
          redirect: Redirect(universal: _chainMetadata.redirectUrl),
        ),
      );
      isInitialize = true;
    } catch (err) {
      debugPrint("Catch wallet initialize error $err");
    }
    return isInitialize;
  }

  @override
  Future<ConnectResponse?> connect() async {
    try {
      ConnectResponse? resp = await wClient.connect(
        requiredNamespaces: {
          _chainMetadata.type: RequiredNamespace(
            chains: [_chainMetadata.chainId], // Ethereum chain
            methods: [_chainMetadata.method], // Requestable Methods
            events: _chainMetadata.events, // Requestable Events
          ),
        },
      );

      return resp;
    } catch (err) {
      debugPrint("Catch wallet connect error $err");
    }
    return null;
  }

  @override
  Future<SessionData?> authorize(
    ConnectResponse resp,
    String unSignedMessage,
  ) async {
    SessionData? sessionData;
    try {
      sessionData = await resp.session.future;
    } catch (err) {
      debugPrint("Catch wallet authorize error $err");
    }
    return sessionData;
  }

  @override
  Future<String?> sendMessageForSigned(
    ConnectResponse resp,
    String walletAddress,
    String topic,
    String unSignedMessage,
  ) async {
    String? signature;
    try {
      Uri? uri = resp.uri;
      if (uri != null) {
        // Now that you have a session, you can request signatures
        final res = await wClient.request(
          topic: topic,
          chainId: _chainMetadata.chainId,
          request: SessionRequestParams(
            method: _chainMetadata.method,
            params: [unSignedMessage, walletAddress],
          ),
        );
        signature = res.toString();
      }
    } catch (err) {
      debugPrint("Catch SendMessageForSigned error $err");
    }
    return signature;
  }

  @override
  Future<bool> onDisplayUri(Uri? uri) async {
    final link = formatNativeUrl(
      WalletConstants.deepLinkMetamask,
      uri.toString(),
    );
    var url = link.toString();
    if (!await canLaunchUrlString(url)) {
      return false;
    }
    return await launchUrlString(url, mode: LaunchMode.externalApplication);
  }

  @override
  Future<void> disconnectWallet({required String topic}) async {
    await wClient.disconnect(
      topic: topic,
      reason: Errors.getSdkError(Errors.USER_DISCONNECTED),
    );
  }
}

Uri? formatNativeUrl(String? deepLink, String wcUri) {
  String safeAppUrl = deepLink ?? "";

  if (deepLink != null && deepLink.isNotEmpty) {
    if (!safeAppUrl.contains('://')) {
      safeAppUrl = deepLink.replaceAll('/', '').replaceAll(':', '');
      safeAppUrl = '$safeAppUrl://';
    }
  }

  String encodedWcUrl = Uri.encodeComponent(wcUri);

  return Uri.parse('$safeAppUrl$encodedWcUrl');
}
