---
description: 
globs: 
alwaysApply: true
---
### **<PERSON>uy <PERSON> Code Dự Án Flutter**

Dựa trên phân tích codebase, đây là các quy tắc và quy ước được đề xuất để duy trì sự nhất quán và chất lượng code.

#### **1. <PERSON><PERSON>u <PERSON>r<PERSON>**

Dự án tuân theo cấu trúc phân lớp rõ ràng. Khi thêm file mới, hãy tuân thủ cấu trúc sau:

-   `lib/`
    -   `core/`: Chứa logic cốt lõi của ứng dụng.
        -   `repository/`: Lớp trừu tượng hóa nguồn dữ liệu (API, database). Mỗi repository nên có một interface (abstract class) và một implementation.
        -   `service/`: Lớp chịu trách nhiệm giao tiếp với các dịch vụ bên ngoài (REST API, Firebase, etc.).
        -   `constant/`: <PERSON><PERSON><PERSON> c<PERSON>c hằng số của ứng dụng.
    -   `cubit/`: <PERSON><PERSON><PERSON> các `Cubit` và `State` cho việc quản lý state. Mỗi feature nên có một thư mục con riêng.
    -   `l10n/`: Chứa các file `.arb` cho đa ngôn ngữ.
    -   `locator/`: Chứa file cấu hình dependency injection (`get_it`).
    -   `model/`: Chứa các lớp model dữ liệu (data classes).
    -   `router/`: Chứa file cấu hình `go_router`.
    -   `screen/`: Chứa các màn hình (UI). Mỗi màn hình/feature lớn nên có một thư mục con.
    -   `utils/`: Chứa các hàm, lớp tiện ích tái sử dụng.
    -   `widget/`: Chứa các widget tái sử dụng trên toàn ứng dụng.

#### **2. Quản Lý Trạng Thái (State Management)**

-   **Sử dụng `flutter_bloc` (Cubit):**
    -   Đối với mỗi màn hình hoặc component có trạng thái phức tạp, hãy tạo một `Cubit` tương ứng trong thư mục `lib/cubit/<feature_name>/`.
    -   Mỗi Cubit phải đi kèm với một `State` class (ví dụ: `AuthCubit` và `AuthState`). `AuthState` nên là một `sealed class` hoặc `abstract class` để định nghĩa các trạng thái khác nhau (e.g., `Initial`, `Loading`, `Success`, `Failure`).
    -   Cung cấp Cubit cho cây widget bằng `BlocProvider`. Sử dụng `BlocProvider.value` khi Cubit đã được khởi tạo từ trước (ví dụ: thông qua `get_it`).
    -   Sử dụng `BlocBuilder` hoặc `BlocListener` để rebuild UI hoặc thực hiện các side-effect khi state thay đổi.

#### **3. Dependency Injection**

-   **Sử dụng `get_it`:**
    -   Tất cả các dependencies (Services, Repositories, Cubits) phải được đăng ký trong file `lib/locator/locator.dart`.
    -   Sử dụng `registerLazySingleton` cho các đối tượng chỉ cần một instance duy nhất và chỉ được khởi tạo khi được gọi lần đầu tiên.
    -   Sử dụng `registerFactory` cho các đối tượng cần một instance mới mỗi khi được yêu cầu.
    -   Để lấy một dependency, sử dụng `serviceLocator<MyType>()` hoặc `GetIt.instance<MyType>()`.

#### **4. Điều Hướng (Navigation)**

-   **Sử dụng `go_router`:**
    -   Tất cả các route phải được định nghĩa trong `lib/router/app_router.dart`.
    -   Sử dụng `enum RouterEnums` để định nghĩa các đường dẫn (path) một cách nhất quán và tránh lỗi chính tả.
    -   Để điều hướng, sử dụng `context.go('/path')`, `context.push('/path')` hoặc các phương thức tương đương của `go_router`.
    -   Khi cần truyền dữ liệu giữa các màn hình, hãy sử dụng tham số `extra` của `GoRoute`.
    -   Sử dụng `ShellRoute` cho các layout có UI chung (ví dụ: Bottom Navigation Bar, Nested Navigation).

#### **5. Lớp API (Service & Repository)**

-   **Service Layer (`lib/core/service/`):**
    -   Chịu trách nhiệm thực hiện các cuộc gọi API bằng `Dio`.
    -   Cấu hình `Dio` (interceptors, base URL, timeouts) được thực hiện trong `lib/locator/locator.dart`.
    -   Mỗi service nên tương ứng với một nhóm các endpoint của API (e.g., `AuthService`, `SocialService`).
-   **Repository Layer (`lib/core/repository/`):**
    -   Là lớp trung gian giữa `Cubit` và `Service`.
    -   `Repository` gọi các phương thức từ `Service` và có thể xử lý, kết hợp dữ liệu từ nhiều nguồn khác nhau trước khi trả về cho `Cubit`.
    -   Luôn tạo một abstract class (interface) cho repository và một class implement nó. Điều này giúp cho việc testing và thay đổi implementation dễ dàng hơn. (e.g., `AuthRepository` và `AuthRepositoryImpl`).
#### **6. Quy Ước Đặt Tên (Naming Conventions)**

-   **Files:** `snake_case_with_lowercase.dart` (e.g., `app_router.dart`, `home_screen.dart`).
-   **Classes:** `PascalCase` (e.g., `HomeScreen`, `AuthCubit`).
-   **Enums:** `PascalCase` (e.g., `RouterEnums`).
-   **Constants:** `camelCase` hoặc `kCamelCase` (e.g., `baseUrl`, `kTokenKey`).
-   **Methods/Functions:** `camelCase` (e.g., `setupLocator`, `getUniqueDeviceId`).

#### **7. Đa Ngôn Ngữ (Localization)**

-   Sử dụng `flutter_localizations` và thư viện `intl`.
-   Các chuỗi văn bản được định nghĩa trong các file `.arb` trong thư mục `lib/l10n/`.
-   Sử dụng class `S` được tạo tự động để truy cập các chuỗi đã dịch (e.g., `S.of(context).helloWorld`). 

#### **8. Hệ Thống Thiết Kế (Design System)**

Để đảm bảo giao diện người dùng nhất quán và dễ bảo trì, hãy tuân thủ các quy tắc sau:

-   **Màu Sắc (Colors):**
    -   **Không sử dụng màu hard-coded:** Thay vì `Color(0xFFFFFFFF)`, hãy sử dụng các màu đã được định nghĩa sẵn.
    -   **Sử dụng màu từ Theme:** Tất cả các màu được định nghĩa trong `lib/widget/colors/colors.dart` dưới dạng extension của `ThemeData`. Truy cập chúng thông qua `context.themeData.primaryGreen500`. Cách tiếp cận này cho phép màu sắc tự động thích ứng với theme (sáng/tối).
    -   **Thêm màu mới:** Khi cần thêm màu mới, hãy khai báo nó trong file `lib/widget/colors/colors.dart` theo cấu trúc đã có.

-   **Kiểu Chữ (Typography):**
    -   **Sử dụng kiểu chữ đã định nghĩa:** Tất cả các `TextStyle` được định nghĩa sẵn trong `lib/widget/colors/text_style.dart` (e.g., `headlineLarge`, `titleMedium`, `bodySmall`).
    -   **Chọn kiểu chữ theo ngữ nghĩa:** Sử dụng các style theo đúng mục đích của chúng (ví dụ: `headlineLarge` cho tiêu đề lớn nhất, `bodyMedium` cho văn bản nội dung chính).
    -   **Tùy chỉnh Style:** Khi cần thay đổi một thuộc tính (như màu sắc), hãy sử dụng phương thức `.copyWith()`. Ví dụ: `Text("Hello", style: bodyMedium.copyWith(color: context.themeData.primaryGreen500))`. Điều này giữ cho các thuộc tính cơ bản (font, size, weight) nhất quán.

-   **Widgets Tái Sử Dụng:**
    -   **Ưu tiên Widget chung:** Trước khi tạo một widget mới, hãy kiểm tra thư mục `lib/widget/` để xem đã có widget nào tương tự có thể tái sử dụng hoặc mở rộng hay chưa (e.g., `Button`, `TextField`).
    -   **Tạo Widget mới:** Khi tạo một widget mới có khả năng tái sử dụng cao, hãy đặt nó vào một thư mục con hợp lý trong `lib/widget/`.
    -   **Tính tùy biến:** Các widget tái sử dụng nên được thiết kế để có thể tùy chỉnh thông qua các tham số truyền vào, nhưng vẫn giữ được phong cách thiết kế chung của ứng dụng.
    -   **Bổ sung Design System:** Nếu trong Design System (các file `lib/widget/colors/colors.dart` hoặc `lib/widget/colors/text_style.dart`) chưa có màu sắc hoặc kiểu chữ phù hợp với nhu cầu mới, bạn PHẢI thêm định nghĩa mới vào các file này trước khi sử dụng trong code. Tuyệt đối không sử dụng trực tiếp giá trị màu hoặc text style chưa được chuẩn hóa. 
