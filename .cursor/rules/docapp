
🧩 Các tính năng chính của ứng dụng Social Service
1. Đăng nhập người dùng
    * Cho phép người dùng đăng nhập bằng tài khoản email (vd: <EMAIL>)
    * Hệ thống hỗ trợ nhiều người dùng test khác nhau.
2. Tải lên phương tiện (Media Upload)
    * Người dùng có thể tải lên hình ảnh hoặc video.
    * <PERSON>u khi tải lên, hệ thống trả về media key để dùng khi tạo bài viết.
3. <PERSON><PERSON><PERSON> bà<PERSON> viết (Post Creation)
    * Hỗ trợ các loại bài viết khác nhau:
        * Vă<PERSON> bản (text)
        * Hình ảnh (image)
        * Video (video)
        * NFT
    * Có thể đính kèm media từ bước upload.
4. <PERSON>ình luận bài viết (Commenting)
    * Người dùng có thể thêm bình luận vào bất kỳ bài viết nào.
5. Tương tác với bài viết (Interaction)
    * Hỗ trợ các hành động:
        * Thích (Like)
        * Chia sẻ lại (Repost)
6. Xem nội dung (Feed & User Posts)
    * Người dùng có thể:
        * Xem bảng tin (feed) tổng hợp.
        * Xem các bài viết của từng người dùng cụ thể.
7. Chỉnh sửa & Xóa bài viết
    * Hỗ trợ cập nhật và xóa bài viết sau khi tạo.
8. Thiết lập quyền riêng tư (Privacy Settings)
    * Có thể chọn quyền hiển thị bài viết theo các mức:
        * Công khai (Public)
        * Bạn bè (Friends)
        * Riêng tư (Private)
        * Tùy chỉnh (Custom)
9. Quản lý phương tiện (Media Operations)
    * Ngoài tải lên, người dùng còn có thể:
        * Xem lại thông tin media (Get)
        * Xóa media đã tải (Delete)
